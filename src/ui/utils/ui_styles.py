"""
UI样式工具模块
提供界面样式相关的通用函数
"""
from PyQt5.QtWidgets import QGroupBox


class UIStyles:
    """UI样式工具类

    提供各种UI样式设置的静态方法，方便在不同模块之间共享使用
    """

    @staticmethod
    def set_group_title_bold(group_box):
        """设置QGroupBox标题为加粗

        Args:
            group_box (QGroupBox): 需要设置标题加粗的QGroupBox实例
        """
        font = group_box.font()
        font.setBold(True)
        group_box.setFont(font)
