"""
主窗口类
"""
import json
import threading
import os
import sys
import shutil
import ctypes
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
                             QMessageBox, QApplication, QSizePolicy, QSplitter,
                             QMenuBar, QMenu, QFileDialog, QTabWidget, QInputDialog,
                             QDialog, QRadioButton, QButtonGroup, QDialogButtonBox,
                             QLabel, QPushButton, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSlot, QSize, QPoint
from PyQt5.QtGui import <PERSON>Font

from api.zlgcan import *
from src.core.can_controller import CANController
from src.core.message_handler import MessageHandler
from src.ui.widgets.device_panel import DevicePanel
from src.ui.widgets.channel_panel import ChannelPanel
from src.ui.widgets.message_panel import MessagePanel
from src.utils.constants import *
from src.utils.paths import DEVICE_INFO_FILE, CONFIG_DIR, ZLGCAN_DLL_FILE, API_DIR
from src.utils.font_manager import font_manager
from src.utils.app_info import APP_NAME, APP_VERSION, APP_DESCRIPTION, DEVELOPER_NAME, DEVELOPER_EMAIL, APP_COPYRIGHT
from src.core.config_manager import ConfigManager, ConfigType
from src.ui.tab_manager import TabManager
from src.utils.msg_hex_calculator import run_calculator  # 导入CAN HEX计算器
from src.ui.dialogs.converter_dialog import run_converter  # 导入进制转换对话框
from src.utils.logger import get_logger
from src.ui.dialogs.changelog_dialog import ChangelogDialog
from src.ui.dialogs import DeviceInfoDialog
from src.core.matrix_manager import MatrixManager

# 创建日志记录器
logger = get_logger("main_window")


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # 创建控制器
        self._can_controller = CANController()

        # 加载设备信息
        self._load_device_info()

        # 初始化当前设备索引 - 始终为0
        self._current_device_index = 0

        self._config = ConfigManager(CONFIG_DIR)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)  # 将标签页设置为中心部件

        # 创建标签页管理器
        self.tab_manager = TabManager(self.tab_widget)

        # 初始化UI
        self._init_ui()
        self._init_connections()

        # 恢复窗口状态
        self._restore_window_state()

    def _load_device_info(self):
        """从dev_info.json加载设备信息"""
        try:
            with open(DEVICE_INFO_FILE, "r") as fd:
                self._dev_info = json.load(fd)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载设备信息文件失败: {str(e)}")
            self._dev_info = {}

    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle(f"{APP_NAME}")
        self.setMinimumSize(WIDGET_WIDTH, WIDGET_HEIGHT)
        menubar = self.menuBar()

        # 添加矩阵管理菜单（新增）
        matrix_menu = menubar.addMenu("矩阵管理")
        matrix_manage_action = matrix_menu.addAction("DBC文件管理")
        matrix_manage_action.triggered.connect(self._open_matrix_manager)

        # 原有菜单
        tool_menu = menubar.addMenu("工具")
        calculator_action = tool_menu.addAction("CAN报文计算器")
        calculator_action.triggered.connect(self._open_hex_calculator)
        converter_action = tool_menu.addAction("进制转换")
        converter_action.triggered.connect(self._open_converter)
        help_menu = menubar.addMenu("帮助")
        help_menu.addAction(font_manager.create_font_action())
        about_action = help_menu.addAction("关于")
        about_action.triggered.connect(self._show_about_dialog)
        changelog_action = help_menu.addAction("更新日志")
        changelog_action.triggered.connect(self._show_changelog_dialog)
        self._init_home_tab()

    def _init_home_tab(self):
        """初始化首页内容"""
        # 获取首页标签页
        home_tab = self.tab_widget.widget(0)  # 首页应该是第一个标签页
        if not home_tab:
            return

        home_layout = QVBoxLayout()
        layout = QHBoxLayout()
        layout.setSpacing(0)  # 移除布局间距
        layout.setContentsMargins(5, 5, 5, 5)  # 设置边距

        # 创建分隔器
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.setChildrenCollapsible(False)  # 禁止完全折叠

        # 创建左侧面板
        left_panel = QWidget()
        left_panel.setMinimumWidth(GRPBOX_WIDTH)

        left_layout = QVBoxLayout()
        left_layout.setSpacing(5)
        left_layout.setContentsMargins(5, 0, 5, 5)

        # 设备面板 - 恢复合理大小
        self.device_panel = DevicePanel()
        self.device_panel.setMinimumWidth(GRPBOX_WIDTH)
        self.device_panel.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.device_panel.setContentsMargins(5, 5, 5, 5)
        self.device_panel.setMinimumHeight(120)  # 设置合适的高度
        self.device_panel.set_device_info(self._dev_info)
        left_layout.addWidget(self.device_panel)

        # 通道面板 - 保持适当的空间
        self.channel_panel = ChannelPanel()
        self.channel_panel.setMinimumWidth(GRPBOX_WIDTH)
        self.channel_panel.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.channel_panel.setContentsMargins(5, 5, 5, 5)
        self.channel_panel.setMinimumHeight(240)  # 调整通道面板高度
        left_layout.addWidget(self.channel_panel)

        # 添加弹性空间前插入固定间距
        left_layout.addSpacing(5)

        left_panel.setLayout(left_layout)

        # 创建消息面板，传入channel_panel和can_controller
        self.message_panel = MessagePanel(
            self.channel_panel, self._can_controller)
        # 设置尺寸策略为可扩展
        self.message_panel.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 添加面板到分隔器
        self.splitter.addWidget(left_panel)
        self.splitter.addWidget(self.message_panel)

        # 将布局放入一个QWidget中
        layout.addWidget(self.splitter)
        layout_widget = QWidget()
        layout_widget.setLayout(layout)
        home_layout.addWidget(layout_widget)
        home_tab.setLayout(home_layout)

    def _init_connections(self):
        """初始化信号连接"""
        # 设备面板信号
        self.device_panel.device_opened.connect(self._on_device_opened)
        self.device_panel.show_device_info_requested.connect(
            self._on_current_dev_info)

        # 通道面板信号 - 修复通道独立控制
        self.channel_panel.channel_opened.connect(self._on_channel_opened)
        self.channel_panel.protocol_changed.connect(
            self.message_panel.on_protocol_changed)

        # 消息面板信号
        self.message_panel.send_message.connect(self._on_send_message)
        self.message_panel.reset_counters.connect(self._on_reset_counters)

        # 消息发送状态信号连接到设备面板和通道面板
        self._can_controller.message_sent.connect(
            self.message_panel.on_message_sent)

    def _restore_window_state(self):
        """恢复窗口状态"""
        try:
            # 从配置加载窗口大小和位置
            window_size = QSize(
                self._config.get_value(
                    ConfigType.WINDOW_LAYOUT, "窗口.宽度", 1024),
                self._config.get_value(ConfigType.WINDOW_LAYOUT, "窗口.高度", 768)
            )
            window_pos = QPoint(
                self._config.get_value(
                    ConfigType.WINDOW_LAYOUT, "窗口.X坐标", 100),
                self._config.get_value(ConfigType.WINDOW_LAYOUT, "窗口.Y坐标", 100)
            )

            # 确保窗口在屏幕范围内
            desktop = QApplication.desktop()
            screen_rect = desktop.availableGeometry()

            # 计算窗口应该显示的位置
            if not screen_rect.contains(window_pos):
                # 计算屏幕中心点
                center = screen_rect.center()
                # 计算窗口左上角应该在的位置
                window_pos = QPoint(
                    center.x() - window_size.width() // 2,
                    center.y() - window_size.height() // 2
                )

            self.resize(window_size)
            self.move(window_pos)

            # 从配置加载分隔器大小
            left_width = self._config.get_value(
                ConfigType.WINDOW_LAYOUT, "分隔器.左侧宽度", 300)
            right_width = self._config.get_value(
                ConfigType.WINDOW_LAYOUT, "分隔器.右侧宽度", 700)
            if hasattr(self, 'splitter'):
                self.splitter.setSizes([left_width, right_width])
        except Exception as e:
            # 如果加载失败，使用默认设置
            print(f"加载窗口状态失败: {e}")
            self.resize(WIDGET_WIDTH, WIDGET_HEIGHT)
            sizes = [GRPBOX_WIDTH, WIDGET_WIDTH - GRPBOX_WIDTH - 20]
            self.splitter.setSizes(sizes)

    def _save_window_state(self):
        """保存窗口状态"""
        # 保存窗口大小和位置
        self._config.set_value(ConfigType.WINDOW_LAYOUT, "窗口.宽度", self.width())
        self._config.set_value(ConfigType.WINDOW_LAYOUT,
                               "窗口.高度", self.height())
        self._config.set_value(ConfigType.WINDOW_LAYOUT, "窗口.X坐标", self.x())
        self._config.set_value(ConfigType.WINDOW_LAYOUT, "窗口.Y坐标", self.y())

        # 保存分隔器大小
        if hasattr(self, 'splitter'):
            sizes = self.splitter.sizes()
            if len(sizes) >= 2:
                self._config.set_value(
                    ConfigType.WINDOW_LAYOUT, "分隔器.左侧宽度", sizes[0])
                self._config.set_value(
                    ConfigType.WINDOW_LAYOUT, "分隔器.右侧宽度", sizes[1])

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            self.tab_manager.save_tabs_state()  # 保存标签页状态

            # 其他关闭逻辑
            if self._can_controller.is_open:
                QMessageBox.warning(self, "警告", "请先关闭设备后再退出程序！")
                event.ignore()
                return

            self._save_window_state()
            event.accept()
        except Exception as e:
            print(f"关闭窗口时出错: {e}")
            event.accept()  # 确保窗口能够关闭

    def _restart_application(self):
        """重启应用程序"""
        try:
            # 保存所有需要保存的状态
            self.tab_manager.save_tabs_state()  # 保存标签页状态
            self._save_window_state()  # 保存窗口状态

            # 关闭主窗口而不触发closeEvent
            self.setAttribute(Qt.WA_DeleteOnClose, False)
            self.close()

            # 使用Python内置的函数重启应用程序
            python = sys.executable
            os.execl(python, python, *sys.argv)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"重启应用程序时出错: {e}")
            # 出错时仍然尝试关闭应用程序
            self.close()

    def _on_splitter_moved(self, pos: int, index: int):
        """分隔器移动事件处理"""
        # 获取左侧面板当前宽度
        left_width = self.splitter.sizes()[0]
        # 动态调整设备面板和通道面板的宽度
        self.device_panel.setMinimumWidth(min(left_width - 10, GRPBOX_WIDTH))
        self.channel_panel.setMinimumWidth(min(left_width - 10, GRPBOX_WIDTH))

    def sizeHint(self) -> QSize:
        """返回建议的窗口大小"""
        return QSize(WIDGET_WIDTH, WIDGET_HEIGHT)

    @pyqtSlot(bool)
    def _on_device_opened(self, is_open: bool):
        """设备打开/关闭处理

        Args:
            is_open: 是否打开设备
        """
        if is_open:
            # 打开设备
            dev_type = self.device_panel.get_device_type()
            dev_idx = 0  # 设备索引始终为0

            if not self._can_controller.open_device(dev_type, dev_idx):
                QMessageBox.critical(self, "错误", "打开设备失败!")
                self.device_panel._on_open_clicked()  # 恢复UI状态
                return

            # 更新设备信息显示
            dev_info = self._can_controller.get_device_info()
            self.device_panel.update_device_info(dev_info)

            # 更新通道信息
            self.channel_panel.update_channel_info(
                self.device_panel.get_current_device_info(), True)

            # 保存当前设备索引
            self._current_device_index = dev_idx
        else:
            # 关闭设备前关闭所有通道
            for i in range(MAX_CHANNEL_NUM):
                if self._can_controller.is_channel_open(i):
                    # 先停止所有线程
                    self.message_panel.stop_all_threads()
                    # 再关闭通道
                    self.channel_panel._on_open_clicked(i)

            # 关闭设备
            self._can_controller.close_device()

            # 更新设备信息显示
            self.device_panel.update_device_info(None)

            # 更新通道信息
            self.channel_panel.update_channel_info(None, False)

            # 禁用发送功能组
            self.message_panel.set_send_group_enabled(False)

            # 重新加载设备信息，确保与dev_info.json同步
            self._refresh_device_info()

    def _refresh_device_info(self):
        """刷新设备信息，确保与dev_info.json同步"""
        # 只有在设备关闭状态下才能刷新设备信息
        if not self._can_controller.is_open:
            self._load_device_info()
            self.device_panel.set_device_info(self._dev_info)

    @pyqtSlot(int, bool)
    def _on_channel_opened(self, chn_idx: int, is_open: bool):
        """通道打开/关闭处理

        Args:
            chn_idx: 通道索引
            is_open: 是否打开
        """
        if is_open:  # 要打开通道
            # 获取控制参数
            protocol_type = self.channel_panel.get_protocol_type(chn_idx)
            can_mode = self.channel_panel.get_channel_mode(chn_idx)

            # 先设置终端电阻的预期状态，以便日志显示
            if hasattr(self._can_controller, 'set_pending_resistance') and self._can_controller.supports_resistance:
                resistance_enabled = self.channel_panel.get_resistance_enabled(
                    chn_idx)
                self._can_controller.set_pending_resistance(
                    chn_idx, resistance_enabled)

            # 初始化通道配置
            chn_cfg = ZCAN_CHANNEL_INIT_CONFIG()

            # 根据API规范处理协议类型
            # ZLG API只支持两种类型：ZCAN_TYPE_CAN(0)和ZCAN_TYPE_CANFD(1)
            # CANFD_TYPE_CANFD_BRS(2)是应用层的概念，实际使用ZCAN_TYPE_CANFD(1)
            # 具体区别在于发送CANFD消息时是否设置BRS标志位
            if protocol_type > 0:  # CANFD 或 CANFD-BRS
                # 无论是否启用BRS，统一使用CANFD类型初始化
                chn_cfg.can_type = ZCAN_TYPE_CANFD  # 使用标准CANFD类型

                # 详细记录实际协议类型和API使用类型
                protocol_name = "CANFD"
                if protocol_type == 2:  # CANFD BRS
                    protocol_name = "CANFD BRS"

                logger.debug(
                    f"通道{chn_idx}初始化为{protocol_name}，使用API类型ZCAN_TYPE_CANFD(1)")
            else:
                chn_cfg.can_type = ZCAN_TYPE_CAN  # 普通CAN
                logger.debug(f"通道{chn_idx}初始化为普通CAN，使用API类型ZCAN_TYPE_CAN(0)")

            # 初始化通道配置结构体
            if protocol_type > 0:  # CANFD 或 CANFD-BRS
                # CANFD配置
                chn_cfg.config.canfd.acc_code = 0
                chn_cfg.config.canfd.acc_mask = 0xFFFFFFFF

                # 获取仲裁段波特率，确保是整数
                abit_timing = int(self.channel_panel.get_baudrate(chn_idx))
                chn_cfg.config.canfd.abit_timing = abit_timing

                # 获取数据段波特率，确保是整数
                dbit_timing = int(
                    self.channel_panel.get_data_baudrate(chn_idx))
                chn_cfg.config.canfd.dbit_timing = dbit_timing

                # 添加日志记录波特率设置
                logger.debug(
                    f"CANFD通道{chn_idx}设置 - 协议类型: {protocol_name}, "
                    f"仲裁段波特率: {abit_timing}, 数据段波特率: {dbit_timing}"
                )

                chn_cfg.config.canfd.brp = 0
                chn_cfg.config.canfd.filter = 0
                chn_cfg.config.canfd.mode = c_ubyte(can_mode)  # 0:正常模式, 1:只听模式
                chn_cfg.config.canfd.pad = 0
                chn_cfg.config.canfd.reserved = 0
            else:  # 普通CAN
                # 常规CAN配置
                chn_cfg.config.can.acc_code = 0
                chn_cfg.config.can.acc_mask = 0xFFFFFFFF
                chn_cfg.config.can.reserved = 0
                # 获取波特率配置
                baud_rate_str = self.channel_panel.get_baudrate(chn_idx)
                if isinstance(baud_rate_str, dict) and "timing0" in baud_rate_str and "timing1" in baud_rate_str:
                    # USBCAN I/II使用timing0/timing1
                    chn_cfg.config.can.timing0 = int(baud_rate_str["timing0"])
                    chn_cfg.config.can.timing1 = int(baud_rate_str["timing1"])
                else:
                    # 其他设备直接使用波特率值
                    chn_cfg.config.can.timing0 = int(baud_rate_str)
                    chn_cfg.config.can.timing1 = 0  # 不使用
                chn_cfg.config.can.filter = 0
                chn_cfg.config.can.mode = c_ubyte(can_mode)  # 0:正常模式, 1:只听模式

            # 初始化通道
            if not self._can_controller.init_channel(chn_idx, chn_cfg):
                QMessageBox.critical(self, "错误", "初始化通道失败!")
                self.channel_panel._on_open_clicked(chn_idx)  # 恢复UI状态
                return

            # 获取并设置终端电阻状态
            try:
                if self._can_controller.supports_resistance:
                    resistance_enabled = self.channel_panel.get_resistance_enabled(
                        chn_idx)
                    self._can_controller.set_resistance(
                        chn_idx, resistance_enabled)
            except Exception as e:
                # 记录错误但继续流程，不中断通道打开
                print(f"设置终端电阻时出错：{e}")

            # 更新通道状态
            self.message_panel.update_channel_state(
                self._current_device_index, chn_idx, True)

            # 更新UI状态 - 传递实际的协议类型，确保消息发送时正确设置BRS
            self.message_panel.set_canfd_enabled(
                protocol_type > 0, protocol_type)
            # 确保发送组控件启用
            self.message_panel.set_send_group_enabled(True)

            # 设置消息接收回调
            self._can_controller.set_msg_received_callback(
                self.message_panel.add_message)

            # 启动接收线程
            self._can_controller.start_receive(chn_idx)
        else:
            # 先停止所有线程
            self.message_panel.stop_all_threads()

            # 关闭通道
            self._can_controller.close_channel(chn_idx)

            # 更新通道状态
            self.message_panel.update_channel_state(
                self._current_device_index, chn_idx, False)

            # 更新UI状态
            # 只有当所有通道都关闭时，才禁用发送功能
            all_channels_closed = True
            for i in range(MAX_CHANNEL_NUM):
                if self._can_controller.is_channel_open(i):
                    all_channels_closed = False
                    break
            if all_channels_closed:
                # 所有通道关闭时禁用发送组控件
                self.message_panel.set_send_group_enabled(False)
                # 清除消息接收回调
                self._can_controller.set_msg_received_callback(None)

    @pyqtSlot(dict)
    def _on_send_message(self, msg_info: Dict[str, Any]):
        """发送CAN消息处理"""
        try:
            # 创建一个新字典用于日志显示，将ID和数据转换为十六进制格式
            log_msg_info = msg_info.copy()

            # 如果ID是整数，转换为十六进制字符串
            if isinstance(log_msg_info.get('id'), int):
                log_msg_info['id'] = f"0x{log_msg_info['id']:X}"

            # 如果数据是列表，转换为十六进制字符串
            if isinstance(log_msg_info.get('data'), list):
                log_msg_info['data'] = [
                    f"0x{x:02X}" for x in log_msg_info['data']]

            logger.info(f"准备发送消息: {log_msg_info}")

            if not self._can_controller.is_device_opened(self._current_device_index):
                QMessageBox.warning(self, "警告", "设备未打开，无法发送消息")
                return

            # 获取消息参数
            dev_index = self._current_device_index
            # 获取通道索引
            chn_index = msg_info.get('channel', 0)

            # 检查通道是否打开
            if not self._can_controller.is_channel_opened(dev_index, chn_index):
                QMessageBox.warning(self, "警告", f"通道{chn_index}未打开，无法发送消息")
                return

            # 处理消息发送
            self._can_controller.send_message(dev_index, chn_index, msg_info)

        except Exception as e:
            logger.error(f"发送消息失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"发送消息失败: {str(e)}")

    @pyqtSlot(int, int)
    def _on_reset_counters(self, dev_idx: int, chn_idx: int):
        """重置计数器"""
        self._can_controller.reset_message_counters(dev_idx, chn_idx)

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        self._save_window_state()

    def moveEvent(self, event):
        """窗口移动事件"""
        super().moveEvent(event)
        self._save_window_state()

    def _show_about_dialog(self):
        """显示关于对话框"""
        # 处理APP_DESCRIPTION中的换行符
        formatted_description = APP_DESCRIPTION.strip().replace("\n", "<br>")

        about_text = f"""
        <h2>{APP_NAME}-v{APP_VERSION}</h2>
        <p>{formatted_description}</p>
        <p><b>开发者:</b> {DEVELOPER_NAME}</p>
        <p><b>邮箱:</b> {DEVELOPER_EMAIL}</p>
        <p>{APP_COPYRIGHT}</p>
        """
        QMessageBox.about(self, "关于", about_text)

    def _show_changelog_dialog(self):
        dialog = ChangelogDialog(self)
        dialog.exec_()

    def _on_current_dev_info(self):
        """显示当前设备信息对话框（仅作为信号槽，不挂菜单）"""
        dialog = DeviceInfoDialog(self)

        # 填充设备信息
        if self._can_controller.is_open:
            # 获取设备信息
            dev_info = self._can_controller.get_device_info()

            # 获取设备类型名称
            dev_type = self.device_panel.get_device_type()
            dev_type_name = ""
            for dev_name, dev_info_dict in self._dev_info.items():
                if dev_info_dict.get("dev_type") == dev_type:
                    dev_type_name = dev_name
                    break

            # 设置设备信息
            dialog.set_device_info(dev_info, dev_type_name)
        else:
            # 设备未连接
            dialog.set_device_info(None, "")

        # 显示对话框
        dialog.exec_()

    def _open_hex_calculator(self):
        """打开CAN HEX计算器"""
        try:
            # 使用msg_hex_calculator模块中的run_calculator函数启动计算器
            self.calculator_window = run_calculator()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动CAN HEX计算器时出错: {str(e)}")

    def _open_converter(self):
        """打开进制转换对话框"""
        try:
            # 使用converter_dialog模块中的run_converter函数启动进制转换对话框
            self.converter_dialog = run_converter()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动进制转换对话框时出错: {str(e)}")

    def _open_matrix_manager(self):
        """打开矩阵管理对话框"""
        try:
            from src.ui.dialogs.matrix_manager_dialog import MatrixManagerDialog
            dialog = MatrixManagerDialog(self)
            result = dialog.exec_()

            # 如果用户点击了确定按钮，提示切换成功
            if result == QDialog.Accepted:
                QMessageBox.information(self, "提示", "矩阵文件切换成功！")
        except Exception as e:
            logger.error(f"打开矩阵管理对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开矩阵管理对话框失败: {str(e)}")

    def showEvent(self, event):
        super().showEvent(event)
        # 在主窗口显示后，检查是否有待解析的DBC文件
        MatrixManager.get_instance().parse_if_needed()
