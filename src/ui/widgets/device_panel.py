"""
设备面板组件
"""
from typing import Dict, Any, Callable
from PyQt5.QtWidgets import (QWidget, QGroupBox, QLabel, QComboBox,
                             QPushButton, QVBoxLayout, QHBoxLayout, QGridLayout, QSizePolicy, QToolButton)
from PyQt5.QtCore import pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from src.utils.constants import *
from src.ui.widgets.style_utils import set_button_state


class DevicePanel(QWidget):
    # 信号定义
    device_opened = pyqtSignal(bool)  # 设备打开/关闭信号
    show_device_info_requested = pyqtSignal()  # 请求显示当前设备信息

    def __init__(self, parent=None):
        super().__init__(parent)
        self._dev_info = {}  # 设备信息字典
        self._is_sending = False  # 是否正在发送消息
        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 设备选择组
        self.dev_group = QGroupBox("设备选择")
        self.dev_group.setFont(QFont("", -1, QFont.Bold))
        dev_layout = QGridLayout()
        dev_layout.setVerticalSpacing(12)  # 增加垂直间距
        dev_layout.setContentsMargins(15, 15, 15, 15)  # 增加内边距

        # 设备类型
        type_label = QLabel("设备类型:")
        type_label.setMinimumHeight(25)  # 设置较大最小高度
        type_label.setMinimumWidth(100)  # 设置较大最小宽度
        dev_layout.addWidget(type_label, 0, 0)

        self.dev_type_combo = QComboBox()
        self.dev_type_combo.setMinimumHeight(28)  # 设置较大最小高度
        dev_layout.addWidget(self.dev_type_combo, 0, 1)

        # 信息按钮
        info_btn = QToolButton(self)
        info_btn.setText("ℹ️")
        info_btn.setToolTip("查看当前设备信息")
        info_btn.setAutoRaise(True)
        info_btn.clicked.connect(self._on_info_clicked)
        dev_layout.addWidget(info_btn, 0, 2)

        # 打开/关闭按钮
        self.open_btn = QPushButton("打开设备")
        self.open_btn.setFixedHeight(35)  # 增大按钮高度
        self.open_btn.setFont(QFont("", 10))  # 设置字体大小
        self.open_btn.clicked.connect(self._on_open_clicked)
        dev_layout.addWidget(self.open_btn, 1, 0, 1, 3)

        # 按钮上下增加间距
        dev_layout.setRowMinimumHeight(1, 15)

        # 初始化按钮样式为未打开（红色）
        set_button_state(self.open_btn, False, '打开设备', '关闭设备')

        # 将设备选择组的布局完成并添加到主布局
        self.dev_group.setLayout(dev_layout)
        layout.addWidget(self.dev_group)

        self.setLayout(layout)

    def set_device_info(self, dev_info: Dict[str, Any]):
        """设置设备信息

        Args:
            dev_info: 设备信息字典
        """
        self._dev_info = dev_info
        self.dev_type_combo.clear()
        self.dev_type_combo.addItems(list(dev_info.keys()))

    def update_device_info(self, info):
        """更新设备信息显示（保留方法以兼容现有代码）

        Args:
            info: 设备信息对象，不再显示但保留以备其他地方使用
        """
        # 具体的信息显示已经移到"当前设备信息"对话框中，此方法保留以兼容接口
        pass

    def _on_open_clicked(self):
        """打开/关闭按钮点击处理"""
        is_open = self.open_btn.text() == "关闭设备"
        set_button_state(self.open_btn, not is_open, '打开设备', '关闭设备')
        self.dev_type_combo.setEnabled(is_open)
        self.device_opened.emit(not is_open)

    def on_message_sending_changed(self, is_sending: bool):
        """处理消息发送状态变化

        Args:
            is_sending: 是否正在发送消息
        """
        self._is_sending = is_sending

        # 如果设备已打开且按钮当前是"关闭设备"状态
        if self.open_btn.text() == "关闭设备":
            if is_sending:
                # 正在发送消息，禁用设备开关按钮
                self.open_btn.setEnabled(False)
                self.open_btn.setToolTip("若要操作设备，请先停止发送消息")
            else:
                # 停止发送消息，启用设备开关按钮
                self.open_btn.setEnabled(True)
                self.open_btn.setToolTip("")

    def get_device_type(self) -> int:
        """获取当前选择的设备类型值"""
        dev_name = self.dev_type_combo.currentText()
        return self._dev_info[dev_name]["dev_type"]

    def get_device_index(self) -> int:
        """获取当前选择的设备索引"""
        return 0  # 始终返回固定值0

    def get_current_device_info(self) -> Dict[str, Any]:
        """获取当前选择的设备信息"""
        return self._dev_info[self.dev_type_combo.currentText()]

    def _on_info_clicked(self):
        """点击信息按钮时发射信号，由主窗口负责弹窗"""
        self.show_device_info_requested.emit()
