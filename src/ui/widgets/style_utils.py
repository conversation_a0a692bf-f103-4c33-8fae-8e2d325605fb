"""
按钮样式工具函数
"""
from PyQt5.QtWidgets import QPushButton

def set_button_state(button: QPushButton, is_open: bool, open_text: str, close_text: str):
    """设置按钮的状态和样式

    Args:
        button: 目标按钮
        is_open: 当前是否为打开状态
        open_text: 打开时显示的文本
        close_text: 关闭时显示的文本
    """
    if is_open:
        button.setText(close_text)
        button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
        """)
    else:
        button.setText(open_text)
        button.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #B71C1C;
            }
        """) 