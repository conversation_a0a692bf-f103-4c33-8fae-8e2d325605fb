"""
通道面板组件
"""
from typing import Dict, Any, List
from PyQt5.QtWidgets import (QWidget, QGroupBox, QLabel, QComboBox,
                             QPushButton, QVBoxLayout, QGridLayout)
from PyQt5.QtCore import pyqtSignal
from PyQt5.QtGui import QFont
import json
from src.utils.logger import get_logger
from src.core.config_manager import ConfigManager, ConfigType
from src.utils.paths import CONFIG_DIR
from src.ui.widgets.style_utils import set_button_state

from src.utils.constants import MAX_CHANNEL_NUM

logger = get_logger()


class ChannelPanel(QWidget):
    # 信号定义
    channel_opened = pyqtSignal(int, bool)  # 通道打开/关闭信号(通道索引, 是否打开)
    protocol_changed = pyqtSignal(int, int)  # 协议变更信号(通道索引, 协议类型)

    def __init__(self, parent=None):
        super().__init__(parent)
        self._chn_info = {}  # 通道信息
        self._chn_groups = []  # 通道组框列表
        self._is_sending = False  # 是否正在发送消息

        # 初始化配置管理器
        self._config = ConfigManager(CONFIG_DIR)

        self._init_ui()

    def _save_channel_config(self, chn_idx: int):
        """保存通道配置

        Args:
            chn_idx: 通道索引
        """
        group = self._chn_groups[chn_idx]

        # 获取各个控件
        mode_combo = group.findChild(QComboBox, f"mode_combo_{chn_idx}")
        protocol_combo = group.findChild(
            QComboBox, f"protocol_combo_{chn_idx}")
        baudrate_combo = group.findChild(
            QComboBox, f"baudrate_combo_{chn_idx}")
        data_baudrate_combo = group.findChild(
            QComboBox, f"data_baudrate_combo_{chn_idx}")
        resistance_combo = group.findChild(
            QComboBox, f"resistance_combo_{chn_idx}")

        # 保存配置
        channel_config = {
            "mode": mode_combo.currentText(),
            "protocol": protocol_combo.currentText(),
            "baudrate": baudrate_combo.currentText(),
            "data_baudrate": data_baudrate_combo.currentText(),
            "resistance": resistance_combo.currentText()
        }

        # 使用ConfigManager保存配置
        self._config.set_value(
            ConfigType.CHANNEL, f"通道状态.通道{chn_idx}", channel_config)

    def _load_channel_config(self, chn_idx: int) -> Dict[str, str]:
        """加载通道配置

        Args:
            chn_idx: 通道索引

        Returns:
            Dict[str, str]: 通道配置
        """
        try:
            # 获取通道组
            group = self._chn_groups[chn_idx]

            # 获取各个控件
            mode_combo = group.findChild(QComboBox, f"mode_combo_{chn_idx}")
            protocol_combo = group.findChild(
                QComboBox, f"protocol_combo_{chn_idx}")
            baudrate_combo = group.findChild(
                QComboBox, f"baudrate_combo_{chn_idx}")
            data_baudrate_combo = group.findChild(
                QComboBox, f"data_baudrate_combo_{chn_idx}")
            resistance_combo = group.findChild(
                QComboBox, f"resistance_combo_{chn_idx}")

            # 从配置中加载通道状态
            channel_config = self._config.get_value(
                ConfigType.CHANNEL, f"通道状态.通道{chn_idx}", {})

            if not channel_config:
                # 使用默认配置
                channel_config = self._config.get_value(
                    ConfigType.CHANNEL, "默认设置", {})

            # 应用配置到UI控件
            if channel_config:
                # 设置工作模式
                if "mode" in channel_config and mode_combo.findText(channel_config["mode"]) != -1:
                    mode_combo.setCurrentText(channel_config["mode"])

                # 设置协议类型
                if "protocol" in channel_config and protocol_combo.findText(channel_config["protocol"]) != -1:
                    protocol_combo.setCurrentText(channel_config["protocol"])

                # 设置波特率
                if "baudrate" in channel_config and baudrate_combo.findText(channel_config["baudrate"]) != -1:
                    baudrate_combo.setCurrentText(channel_config["baudrate"])

                # 设置数据域波特率
                if "data_baudrate" in channel_config and data_baudrate_combo.findText(channel_config["data_baudrate"]) != -1:
                    data_baudrate_combo.setCurrentText(
                        channel_config["data_baudrate"])
                # 如果没有数据域波特率配置但协议是CANFD或CANFD BRS，则默认设置为2M
                elif protocol_combo.currentText() != "CAN" and data_baudrate_combo.isEnabled():
                    index = data_baudrate_combo.findText("2M")
                    if index >= 0:
                        data_baudrate_combo.setCurrentIndex(index)

                # 设置终端电阻
                if "resistance" in channel_config and resistance_combo.findText(channel_config["resistance"]) != -1:
                    resistance_combo.setCurrentText(
                        channel_config["resistance"])

            return channel_config

        except Exception as e:
            logger.error(f"加载通道{chn_idx}配置失败: {e}")
            return {}

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setSpacing(10)  # 设置垂直间距

        # 创建两个通道的配置组
        for i in range(MAX_CHANNEL_NUM):
            group = self._create_channel_group(i)
            self._chn_groups.append(group)
            layout.addWidget(group)

        self.setLayout(layout)

    def _create_channel_group(self, chn_idx: int) -> QGroupBox:
        """创建通道配置组

        Args:
            chn_idx: 通道索引

        Returns:
            QGroupBox: 通道配置组框
        """
        group = QGroupBox(f"CAN{chn_idx}通道配置")
        group.setFont(QFont("", -1, QFont.Bold))

        layout = QGridLayout()

        # 工作模式
        mode_label = QLabel("工作模式:")
        mode_label.setMinimumWidth(100)  # 设置最小宽度
        layout.addWidget(mode_label, 0, 0)
        mode_combo = QComboBox()
        mode_combo.setEnabled(False)
        mode_combo.setObjectName(f"mode_combo_{chn_idx}")
        layout.addWidget(mode_combo, 0, 1)

        # CAN协议
        protocol_label = QLabel("CAN协议:")
        protocol_label.setMinimumWidth(100)  # 设置最小宽度
        layout.addWidget(protocol_label, 1, 0)
        protocol_combo = QComboBox()
        protocol_combo.setEnabled(False)
        protocol_combo.setObjectName(f"protocol_combo_{chn_idx}")
        protocol_combo.currentIndexChanged.connect(
            lambda: self._on_protocol_changed(chn_idx))
        layout.addWidget(protocol_combo, 1, 1)

        # 波特率
        baudrate_label = QLabel("仲裁域波特率:")
        baudrate_label.setMinimumWidth(110)  # 设置最小宽度，较长文本需要更大宽度
        layout.addWidget(baudrate_label, 2, 0)
        baudrate_combo = QComboBox()
        baudrate_combo.setEnabled(False)
        baudrate_combo.setObjectName(f"baudrate_combo_{chn_idx}")
        layout.addWidget(baudrate_combo, 2, 1)

        # 数据域波特率
        data_baudrate_label = QLabel("数据域波特率:")
        data_baudrate_label.setMinimumWidth(110)  # 设置最小宽度，较长文本需要更大宽度
        layout.addWidget(data_baudrate_label, 3, 0)
        data_baudrate_combo = QComboBox()
        data_baudrate_combo.setEnabled(False)
        data_baudrate_combo.setObjectName(f"data_baudrate_combo_{chn_idx}")
        layout.addWidget(data_baudrate_combo, 3, 1)

        # 终端电阻
        resistance_label = QLabel("终端电阻:")
        resistance_label.setMinimumWidth(100)  # 设置最小宽度
        layout.addWidget(resistance_label, 4, 0)
        resistance_combo = QComboBox()
        resistance_combo.setEnabled(False)
        resistance_combo.setObjectName(f"resistance_combo_{chn_idx}")
        layout.addWidget(resistance_combo, 4, 1)

        # 打开/关闭按钮
        open_btn = QPushButton("打开通道")
        open_btn.setEnabled(False)
        open_btn.setObjectName(f"open_btn_{chn_idx}")
        open_btn.clicked.connect(lambda: self._on_open_clicked(chn_idx))
        layout.addWidget(open_btn, 5, 0, 1, 2)

        # 初始化按钮样式为未打开（红色）
        set_button_state(open_btn, False, '打开通道', '关闭通道')

        group.setLayout(layout)
        return group

    def _on_protocol_changed(self, chn_idx: int):
        """CAN协议切换处理

        Args:
            chn_idx: 通道索引
        """
        group = self._chn_groups[chn_idx]
        protocol_combo = group.findChild(
            QComboBox, f"protocol_combo_{chn_idx}")
        data_baudrate_combo = group.findChild(
            QComboBox, f"data_baudrate_combo_{chn_idx}")

        # 发送协议变更信号
        self.protocol_changed.emit(chn_idx, protocol_combo.currentIndex())

        # 只有在CANFD模式下才启用数据域波特率设置
        if protocol_combo.currentText() == "CAN":
            data_baudrate_combo.clear()
            data_baudrate_combo.setEnabled(False)
        else:  # CANFD 或 CANFD BRS
            data_baudrate_combo.clear()
            if self._chn_info.get("is_canfd", False):
                data_baudrate_combo.addItems(
                    list(self._chn_info["data_baudrate"].keys()))
                data_baudrate_combo.setEnabled(True)
                # 默认选择2M波特率
                index = data_baudrate_combo.findText("2M")
                if index >= 0:
                    data_baudrate_combo.setCurrentIndex(index)

    def update_channel_info(self, dev_info: Dict[str, Any], is_enabled: bool):
        """更新通道信息

        Args:
            dev_info: 设备信息字典
            is_enabled: 是否启用
        """
        if is_enabled and dev_info:
            self._chn_info = dev_info["chn_info"]
            chn_num = dev_info["chn_num"]

            # 更新每个通道的配置
            for i in range(MAX_CHANNEL_NUM):
                group = self._chn_groups[i]
                # 只有在通道数量范围内的通道才启用
                enabled = i < chn_num

                # 获取各个控件
                mode_combo = group.findChild(QComboBox, f"mode_combo_{i}")
                protocol_combo = group.findChild(
                    QComboBox, f"protocol_combo_{i}")
                baudrate_combo = group.findChild(
                    QComboBox, f"baudrate_combo_{i}")
                data_baudrate_combo = group.findChild(
                    QComboBox, f"data_baudrate_combo_{i}")
                resistance_combo = group.findChild(
                    QComboBox, f"resistance_combo_{i}")
                open_btn = group.findChild(QPushButton, f"open_btn_{i}")

                if enabled:
                    # 更新工作模式
                    mode_combo.clear()
                    mode_combo.addItems(["正常模式", "只听模式"])
                    mode_combo.setEnabled(True)

                    # 更新CAN协议
                    protocol_combo.clear()
                    if self._chn_info["is_canfd"]:
                        protocol_combo.addItems(["CAN", "CANFD", "CANFD BRS"])
                    else:
                        protocol_combo.addItems(["CAN"])
                    protocol_combo.setEnabled(True)

                    # 更新波特率
                    baudrate_combo.clear()
                    baudrate_combo.addItems(
                        list(self._chn_info["baudrate"].keys()))
                    baudrate_combo.setEnabled(True)

                    # 更新数据域波特率
                    data_baudrate_combo.clear()
                    if self._chn_info["is_canfd"]:
                        data_baudrate_combo.addItems(
                            list(self._chn_info["data_baudrate"].keys()))
                        # 只有在选择CANFD时才启用
                        data_baudrate_combo.setEnabled(
                            protocol_combo.currentText() != "CAN")
                        # 当选择CANFD或CANFD BRS时，默认选择2M波特率
                        if protocol_combo.currentText() != "CAN":
                            index = data_baudrate_combo.findText("2M")
                            if index >= 0:
                                data_baudrate_combo.setCurrentIndex(index)
                    else:
                        data_baudrate_combo.setEnabled(False)

                    # 更新终端电阻
                    resistance_combo.clear()
                    resistance_combo.addItems(["启用", "禁用"])
                    resistance_combo.setEnabled(self._chn_info["sf_res"])

                    open_btn.setEnabled(True)

                    # 加载上次的配置
                    self._load_channel_config(i)

                    # 根据加载的协议类型设置数据域波特率的可用性
                    if protocol_combo.currentText() == "CAN":
                        data_baudrate_combo.clear()
                        data_baudrate_combo.setEnabled(False)
                else:
                    # 禁用所有控件
                    mode_combo.setEnabled(False)
                    protocol_combo.setEnabled(False)
                    baudrate_combo.clear()
                    baudrate_combo.setEnabled(False)
                    data_baudrate_combo.clear()
                    data_baudrate_combo.setEnabled(False)
                    resistance_combo.setEnabled(False)
                    open_btn.setEnabled(False)
        else:
            # 在设备关闭前保存所有通道的配置
            if self._chn_info:  # 只有当之前有设备信息时才保存
                for i in range(MAX_CHANNEL_NUM):
                    group = self._chn_groups[i]
                    open_btn = group.findChild(QPushButton, f"open_btn_{i}")
                    if open_btn.text() == "关闭通道":  # 通道处于打开状态
                        self._save_channel_config(i)

            self._chn_info = {}
            # 禁用所有通道的所有控件并清空选项
            for i in range(MAX_CHANNEL_NUM):
                group = self._chn_groups[i]

                # 清空并禁用工作模式
                mode_combo = group.findChild(QComboBox, f"mode_combo_{i}")
                mode_combo.clear()  # 完全清空选项
                mode_combo.addItems(["正常模式", "只听模式"])  # 重新添加基本选项
                mode_combo.setCurrentIndex(-1)  # 设置为无选择状态
                mode_combo.setEnabled(False)

                # 清空并禁用CAN协议
                protocol_combo = group.findChild(
                    QComboBox, f"protocol_combo_{i}")
                protocol_combo.clear()  # 完全清空选项
                protocol_combo.setEnabled(False)

                # 清空并禁用波特率
                baudrate_combo = group.findChild(
                    QComboBox, f"baudrate_combo_{i}")
                baudrate_combo.clear()
                baudrate_combo.setPlaceholderText("")  # 清空占位符文本
                baudrate_combo.setEnabled(False)

                # 清空并禁用数据域波特率
                data_baudrate_combo = group.findChild(
                    QComboBox, f"data_baudrate_combo_{i}")
                data_baudrate_combo.clear()
                data_baudrate_combo.setPlaceholderText("")  # 清空占位符文本
                data_baudrate_combo.setEnabled(False)

                # 清空并禁用终端电阻
                resistance_combo = group.findChild(
                    QComboBox, f"resistance_combo_{i}")
                resistance_combo.clear()  # 完全清空选项
                resistance_combo.addItems(["启用", "禁用"])  # 重新添加基本选项
                resistance_combo.setCurrentIndex(-1)  # 设置为无选择状态
                resistance_combo.setEnabled(False)

                # 禁用打开按钮并重置状态
                open_btn = group.findChild(QPushButton, f"open_btn_{i}")
                open_btn.setText("打开通道")  # 重置按钮文本
                open_btn.setEnabled(False)

    def _on_open_clicked(self, chn_idx: int):
        """通道打开按钮点击处理

        Args:
            chn_idx: 通道索引
        """
        group = self._chn_groups[chn_idx]
        open_btn = group.findChild(QPushButton, f"open_btn_{chn_idx}")

        # 获取通道当前状态
        is_current_open = open_btn.text() == "关闭通道"

        set_button_state(open_btn, not is_current_open, '打开通道', '关闭通道')

        if not is_current_open:  # 要打开通道
            # 禁用该通道的所有配置控件
            for combo in group.findChildren(QComboBox):
                combo.setEnabled(False)

            # 发送通道打开信号
            self.channel_opened.emit(chn_idx, True)

            # 保存通道配置
            self._save_channel_config(chn_idx)
        else:  # 要关闭通道
            # 恢复该通道的配置控件状态
            mode_combo = group.findChild(QComboBox, f"mode_combo_{chn_idx}")
            protocol_combo = group.findChild(
                QComboBox, f"protocol_combo_{chn_idx}")
            baudrate_combo = group.findChild(
                QComboBox, f"baudrate_combo_{chn_idx}")
            data_baudrate_combo = group.findChild(
                QComboBox, f"data_baudrate_combo_{chn_idx}")
            resistance_combo = group.findChild(
                QComboBox, f"resistance_combo_{chn_idx}")

            mode_combo.setEnabled(True)
            protocol_combo.setEnabled(True)  # 恢复CAN协议控件状态
            baudrate_combo.setEnabled(True)
            if self._chn_info.get("is_canfd", False):
                data_baudrate_combo.setEnabled(True)
            if self._chn_info.get("sf_res", False):
                resistance_combo.setEnabled(True)

            # 发送通道关闭信号
            self.channel_opened.emit(chn_idx, False)

        # 如果是打开通道，打开后根据消息发送状态设置按钮状态
        if not is_current_open and self._is_sending:
            open_btn.setEnabled(False)
            open_btn.setToolTip("若要操作设备，请先停止发送消息")
        # 如果是关闭通道并且不在发送消息，确保按钮可用
        elif is_current_open and not self._is_sending:
            open_btn.setEnabled(True)
            open_btn.setToolTip("")

    def get_channel_mode(self, chn_idx: int) -> int:
        """获取指定通道的工作模式

        Args:
            chn_idx: 通道索引

        Returns:
            int: 工作模式
        """
        mode_combo = self._chn_groups[chn_idx].findChild(
            QComboBox, f"mode_combo_{chn_idx}")
        return mode_combo.currentIndex()

    def get_baudrate(self, chn_idx: int) -> str:
        """获取指定通道的波特率值

        Args:
            chn_idx: 通道索引

        Returns:
            str: 波特率值
        """
        baudrate_combo = self._chn_groups[chn_idx].findChild(
            QComboBox, f"baudrate_combo_{chn_idx}")
        return self._chn_info["baudrate"][baudrate_combo.currentText()]

    def get_data_baudrate(self, chn_idx: int) -> str:
        """获取指定通道的数据域波特率值

        Args:
            chn_idx: 通道索引

        Returns:
            str: 数据域波特率值
        """
        if not self._chn_info.get("is_canfd", False):
            return ""
        data_baudrate_combo = self._chn_groups[chn_idx].findChild(
            QComboBox, f"data_baudrate_combo_{chn_idx}")
        return self._chn_info["data_baudrate"][data_baudrate_combo.currentText()]

    def get_resistance_enabled(self, chn_idx: int) -> bool:
        """获取指定通道的终端电阻是否启用

        Args:
            chn_idx: 通道索引

        Returns:
            bool: 是否启用
        """
        if not self._chn_info.get("sf_res", False):
            return False
        resistance_combo = self._chn_groups[chn_idx].findChild(
            QComboBox, f"resistance_combo_{chn_idx}")
        return resistance_combo.currentIndex() == 0

    def get_protocol_type(self, chn_idx: int) -> int:
        """获取指定通道的CAN协议类型

        Args:
            chn_idx: 通道索引

        Returns:
            int: 协议类型（0: CAN, 1: CANFD, 2: CANFD BRS）
        """
        protocol_combo = self._chn_groups[chn_idx].findChild(
            QComboBox, f"protocol_combo_{chn_idx}")
        return protocol_combo.currentIndex()

    @property
    def is_canfd(self) -> bool:
        """是否支持CANFD"""
        return self._chn_info.get("is_canfd", False)

    def _on_can_type_changed(self, index: int):
        """CAN类型改变处理

        Args:
            index: 当前选择的索引
        """
        # 发送协议变更信号
        self.protocol_changed.emit(self._channel_index, index)

        # 更新波特率选项
        # ... existing code ...

    def on_message_sending_changed(self, is_sending: bool):
        """处理消息发送状态变化

        Args:
            is_sending: 是否正在发送消息
        """
        self._is_sending = is_sending

        # 更新所有通道按钮的状态
        for chn_idx, group in enumerate(self._chn_groups):
            open_btn = group.findChild(QPushButton, f"open_btn_{chn_idx}")
            if open_btn:
                if open_btn.text() == "关闭通道":  # 通道已打开状态
                    if is_sending:
                        # 正在发送消息，禁用通道开关按钮
                        open_btn.setEnabled(False)
                        open_btn.setToolTip("若要操作设备，请先停止发送消息")
                    else:
                        # 停止发送消息，启用通道开关按钮
                        open_btn.setEnabled(True)
                        open_btn.setToolTip("")
