"""
消息面板组件
"""
from typing import Dict, Any, List, Optional
from PyQt5.QtWidgets import (QWidget, QGroupBox, QLabel, QComboBox,
                             QPushButton, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLineEdit, QCheckBox, QTreeWidget, QTreeWidgetItem,
                             QHeaderView, QSizePolicy, QMessageBox, QApplication)
from PyQt5.QtCore import (pyqtSignal, Qt, QThread, QTimer, QMetaObject,
                          Q_ARG, pyqtSlot)
from PyQt5.QtGui import QFont
import threading
import time
from datetime import datetime
import json

from src.utils.constants import *
from src.utils.paths import CONFIG_DIR
from src.core.message_sender import MessageSender
from src.core.message_filter import MessageFilter
from src.core.channel_manager import ChannelManager
from src.core.message_display import MessageDisplayManager
from src.core.config_manager import ConfigManager, ConfigType
from src.utils.logger import get_logger

# 创建日志记录器
logger = get_logger("message_panel")

# 列宽常量定义
MSGCNT_WIDTH = 84    # 序号列宽
MSGCHN_WIDTH = 123   # 通道列宽
MSGID_WIDTH = 80     # 帧ID列宽
MSGDIR_WIDTH = 60    # 方向列宽
MSGINFO_WIDTH = 217  # 帧信息列宽
MSGLEN_WIDTH = 60    # 长度列宽


class MessagePanel(QWidget):
    # 信号定义
    send_message = pyqtSignal(dict)  # 发送消息信号
    reset_counters = pyqtSignal(int, int)  # 重置计数器信号(dev_idx, chn_idx)
    column_resized = pyqtSignal()  # 列宽改变信号
    sending_state_changed = pyqtSignal(bool)  # 发送状态变更信号，True表示正在发送，False表示停止发送

    # 列名称映射
    COLUMN_NAMES = {
        0: "序号",
        1: "时间",
        2: "通道",
        3: "帧ID",
        4: "方向",
        5: "帧信息",
        6: "长度",
        7: "数据"
    }

    def __init__(self, channel_panel, can_controller, parent=None):
        super().__init__(parent)
        self._is_closing = False  # 添加关闭标志
        # 修改为字典，记录已打开的通道 {(dev_idx, chn_idx): protocol_type}
        self._opened_channels = {}
        self._current_device = 0  # 设备索引始终为0
        self._channel_panel = channel_panel  # 保存对通道面板的引用
        self._can_controller = can_controller  # 保存CAN控制器实例

        # 初始化配置管理器
        self._config = ConfigManager(CONFIG_DIR)

        # 计数器初始化
        self._tx_count = 0  # 发送计数
        self._rx_count = 0  # 接收计数
        self._msg_seq = 0   # 消息序号计数器
        self._counter_lock = threading.Lock()  # 计数器锁

        # 消息缓冲区
        self._msg_buffer = []
        self._buffer_lock = threading.Lock()
        self._max_buffer_size = self._config.get_value(
            ConfigType.MESSAGE_PANEL, "显示设置.最大显示条数", 1000)

        # UI更新定时器
        self._update_timer = QTimer()
        self._update_timer.timeout.connect(self._update_display)
        self._update_timer.setInterval(100)  # 100ms更新一次
        self._update_timer.start()

        # 创建管理器实例
        self._message_sender = MessageSender()
        self._message_filter = MessageFilter()
        self._channel_manager = ChannelManager()
        self._message_display = MessageDisplayManager()

        # 将消息发送管理器移动到主线程
        self._message_sender.moveToThread(QThread.currentThread())

        # 连接信号
        self._message_sender.send_error.connect(self._on_send_error)
        self._message_sender.send_complete.connect(self._on_send_complete)
        self._channel_manager.channel_state_changed.connect(
            self._on_channel_state_changed)
        self._message_display.message_added.connect(self._on_message_added)
        self._message_display.counters_updated.connect(
            self._on_counters_updated)
        self._message_display.counters_reset.connect(self._on_counters_reset)

        # 过滤设置
        self._is_paused = False  # 是否暂停刷新
        self._filter_channel = self._config.get_value(
            ConfigType.MESSAGE_PANEL, "过滤器.默认通道", "全部")  # 通道过滤
        self._filter_id = ""  # ID过滤
        self._filter_direction = self._config.get_value(
            ConfigType.MESSAGE_PANEL, "过滤器.默认方向", "全部")  # 方向过滤

        self._init_ui()

        # 确保所有定时器都在主线程中运行
        QTimer.singleShot(0, self._ensure_timers_in_main_thread)

    def _ensure_timers_in_main_thread(self):
        """确保所有定时器都在主线程中运行"""
        # 将所有定时器移动到主线程
        for child in self.findChildren(QTimer):
            child.moveToThread(QThread.currentThread())

    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 设置关闭标志
            self._is_closing = True

            # 停止UI更新定时器
            self._update_timer.stop()

            # 停止所有其他定时器
            for child in self.findChildren(QTimer):
                if child != self._update_timer:
                    child.stop()

            # 断开所有信号连接
            try:
                # 先断开消息发送器的信号连接
                self._message_sender.disconnect_all_signals()

                # 断开其他信号连接
                self._channel_manager.channel_state_changed.disconnect()
                self._message_display.message_added.disconnect()
                self._message_display.counters_updated.disconnect()
                self._message_display.counters_reset.disconnect()
            except Exception:
                pass  # 忽略断开信号连接时的异常

            # 如果发送按钮处于选中状态，取消选中并等待发送停止
            if self.send_btn.isChecked():
                self.send_btn.setChecked(False)
                # 等待一小段时间确保发送停止信号被处理
                time.sleep(0.1)

            # 停止消息发送
            self._message_sender.stop_sending()

            # 等待所有事件处理完成
            for _ in range(5):  # 最多等待5次
                QApplication.processEvents()
                time.sleep(0.1)  # 每次等待100ms

            # 接受关闭事件
            event.accept()

        except Exception as e:
            logger.error(f"关闭窗口异常: {e}", exc_info=True)
            event.accept()  # 确保窗口能够关闭

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setSpacing(5)  # 减小布局间距

        # 消息显示组
        self.msg_display_group = QGroupBox("报文显示")
        self.msg_display_group.setFont(QFont("", -1, QFont.Bold))
        display_layout = QVBoxLayout()
        display_layout.setSpacing(5)

        # 添加过滤控制面板
        filter_layout = QHBoxLayout()
        filter_layout.setSpacing(15)  # 增加组件组之间的间距
        filter_layout.setContentsMargins(5, 5, 5, 5)  # 设置边距

        # 暂停刷新
        self.pause_btn = QPushButton("暂停刷新")
        self.pause_btn.setCheckable(True)  # 设置为可切换按钮
        self.pause_btn.setFixedWidth(80)  # 设置固定宽度
        self.pause_btn.clicked.connect(self._on_pause_clicked)
        filter_layout.addWidget(self.pause_btn)

        # 通道过滤
        channel_filter_layout = QHBoxLayout()
        channel_filter_layout.setSpacing(5)  # 增加标签和下拉框的间距
        label = QLabel("通道:")
        label.setFixedWidth(80)  # 增加标签宽度，从45改为80
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)  # 右对齐
        channel_filter_layout.addWidget(label)
        self.channel_filter_combo = QComboBox()
        self.channel_filter_combo.addItem("全部")
        self.channel_filter_combo.setFixedWidth(120)  # 设置固定宽度
        self.channel_filter_combo.currentTextChanged.connect(
            self._on_filter_changed)
        channel_filter_layout.addWidget(self.channel_filter_combo)
        channel_filter_widget = QWidget()
        channel_filter_widget.setLayout(channel_filter_layout)
        filter_layout.addWidget(channel_filter_widget)

        # ID过滤
        id_filter_layout = QHBoxLayout()
        id_filter_layout.setSpacing(0)  # 彻底去除标签和输入框的间距
        label = QLabel("ID(hex):")
        # 不设置fixedWidth，让其自适应内容
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)  # 右对齐
        id_filter_layout.addWidget(label)
        self.id_filter_edit = QLineEdit()
        self.id_filter_edit.setFixedWidth(80)  # 保持输入框宽度
        self.id_filter_edit.setAlignment(Qt.AlignLeft)  # 左对齐
        self.id_filter_edit.textChanged.connect(self._on_filter_changed)
        id_filter_layout.addWidget(self.id_filter_edit)
        id_filter_widget = QWidget()
        id_filter_widget.setLayout(id_filter_layout)
        id_filter_widget.setContentsMargins(0, 0, 0, 0)  # 去除widget的margin
        filter_layout.addWidget(id_filter_widget)

        # 方向过滤
        direction_filter_layout = QHBoxLayout()
        direction_filter_layout.setSpacing(5)  # 增加标签和下拉框的间距
        label = QLabel("方向:")
        label.setFixedWidth(80)  # 增加标签宽度，从45改为80
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)  # 右对齐
        direction_filter_layout.addWidget(label)
        self.direction_filter_combo = QComboBox()
        self.direction_filter_combo.addItems(["全部", "发送", "接收"])
        self.direction_filter_combo.setFixedWidth(80)  # 设置固定宽度
        self.direction_filter_combo.currentTextChanged.connect(
            self._on_filter_changed)
        direction_filter_layout.addWidget(self.direction_filter_combo)
        direction_filter_widget = QWidget()
        direction_filter_widget.setLayout(direction_filter_layout)
        filter_layout.addWidget(direction_filter_widget)

        # 重置筛选按钮
        self.reset_filter_btn = QPushButton("重置筛选")
        self.reset_filter_btn.setFixedWidth(80)  # 设置固定宽度
        self.reset_filter_btn.clicked.connect(self._on_reset_filter_clicked)
        filter_layout.addWidget(self.reset_filter_btn)

        # 添加弹性空间
        filter_layout.addStretch()

        # 设置样式
        # 注意：不再手动设置字体大小，而是使用全局样式表
        style_sheet = """
            QPushButton {
                padding: 3px;
            }
            QComboBox {
                padding: 3px;
            }
            QLineEdit {
                padding: 3px;
            }
        """
        self.pause_btn.setStyleSheet(style_sheet)
        self.channel_filter_combo.setStyleSheet(style_sheet)
        self.id_filter_edit.setStyleSheet(style_sheet)
        self.direction_filter_combo.setStyleSheet(style_sheet)
        self.reset_filter_btn.setStyleSheet(style_sheet)

        display_layout.addLayout(filter_layout)

        # 消息列表
        self.msg_tree = QTreeWidget()
        self.msg_tree.setColumnCount(8)  # 增加时间列
        self.msg_tree.setHeaderLabels(
            ["序号", "时间", "通道", "帧ID", "方向", "帧信息", "长度", "数据"])

        # 设置列宽
        header = self.msg_tree.header()
        # 允许用户调整列宽
        header.setSectionResizeMode(QHeaderView.Interactive)
        # 数据列自动调整
        header.setStretchLastSection(True)

        # 恢复保存的列宽
        self._restore_column_widths()

        # 连接列宽改变信号
        header.sectionResized.connect(self._on_column_resized)

        # 设置尺寸策略
        self.msg_tree.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Expanding)

        display_layout.addWidget(self.msg_tree)

        # 计数器和清空按钮
        counter_layout = QHBoxLayout()
        counter_layout.setSpacing(10)  # 组件间距

        # 发送计数
        tx_layout = QHBoxLayout()
        tx_layout.setSpacing(5)
        tx_label = QLabel("发送帧数:")
        tx_label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        tx_layout.addWidget(tx_label)
        self.tx_cnt_label = QLabel("0")
        self.tx_cnt_label.setFixedWidth(100)  # 设置计数值显示宽度
        self.tx_cnt_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # 左对齐
        tx_layout.addWidget(self.tx_cnt_label)
        counter_layout.addLayout(tx_layout)

        # 接收计数
        rx_layout = QHBoxLayout()
        rx_layout.setSpacing(5)
        rx_label = QLabel("接收帧数:")
        rx_label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        rx_layout.addWidget(rx_label)
        self.rx_cnt_label = QLabel("0")
        self.rx_cnt_label.setFixedWidth(100)  # 设置计数值显示宽度
        self.rx_cnt_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # 左对齐
        rx_layout.addWidget(self.rx_cnt_label)
        counter_layout.addLayout(rx_layout)

        # 清空按钮
        self.clear_btn = QPushButton("清空")
        self.clear_btn.clicked.connect(self._on_clear_clicked)
        counter_layout.addWidget(self.clear_btn)
        counter_layout.addStretch()

        display_layout.addLayout(counter_layout)
        self.msg_display_group.setLayout(display_layout)
        layout.addWidget(self.msg_display_group)

        # 消息发送组
        self.msg_send_group = QGroupBox("自定义报文发送")
        self.msg_send_group.setFont(QFont("", -1, QFont.Bold))
        send_layout = QGridLayout()
        send_layout.setSpacing(5)  # 组件内部间距

        # 第一行 - 通道选择和发送方式
        # 通道选择组
        channel_layout = QHBoxLayout()
        channel_layout.setSpacing(2)
        channel_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("通道:")
        label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        channel_layout.addWidget(label)
        self.channel_combo = QComboBox()
        # 初始化时不添加任何选项，等待通道打开时再添加
        self.channel_combo.setSizeAdjustPolicy(QComboBox.AdjustToContents)
        self.channel_combo.currentTextChanged.connect(
            self._on_channel_changed)  # 添加通道切换处理
        channel_layout.addWidget(self.channel_combo)
        channel_widget = QWidget()
        channel_widget.setLayout(channel_layout)
        send_layout.addWidget(channel_widget, 0, 0)

        # 发送方式组
        send_type_layout = QHBoxLayout()
        send_type_layout.setSpacing(2)
        send_type_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("发送方式:")
        label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        send_type_layout.addWidget(label)
        self.send_type_combo = QComboBox()
        self.send_type_combo.addItems(["正常发送", "单次发送", "自发自收"])
        self.send_type_combo.setSizeAdjustPolicy(
            QComboBox.AdjustToContents)  # 设置自适应内容
        self.send_type_combo.currentTextChanged.connect(
            self._on_send_type_changed)  # 添加发送方式改变处理
        send_type_layout.addWidget(self.send_type_combo)
        send_type_widget = QWidget()
        send_type_widget.setLayout(send_type_layout)
        send_layout.addWidget(send_type_widget, 0, 2)

        # 第二行
        # 帧类型组
        frame_type_layout = QHBoxLayout()
        frame_type_layout.setSpacing(2)
        frame_type_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("帧类型:")
        label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        frame_type_layout.addWidget(label)
        self.frame_type_combo = QComboBox()
        self.frame_type_combo.addItems(["标准帧", "扩展帧"])
        self.frame_type_combo.setSizeAdjustPolicy(
            QComboBox.AdjustToContents)  # 设置自适应内容
        frame_type_layout.addWidget(self.frame_type_combo)
        frame_type_widget = QWidget()
        frame_type_widget.setLayout(frame_type_layout)
        send_layout.addWidget(frame_type_widget, 0, 4)

        # 帧格式组
        frame_format_layout = QHBoxLayout()
        frame_format_layout.setSpacing(2)
        frame_format_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("帧格式:")
        label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        frame_format_layout.addWidget(label)
        self.frame_format_combo = QComboBox()
        self.frame_format_combo.addItems(["数据帧", "远程帧"])
        self.frame_format_combo.setSizeAdjustPolicy(
            QComboBox.AdjustToContents)  # 设置自适应内容
        self.frame_format_combo.currentIndexChanged.connect(
            self._on_frame_format_changed)
        frame_format_layout.addWidget(self.frame_format_combo)
        frame_format_widget = QWidget()
        frame_format_widget.setLayout(frame_format_layout)
        send_layout.addWidget(frame_format_widget, 0, 6)

        # CAN类型组
        can_type_layout = QHBoxLayout()
        can_type_layout.setSpacing(2)
        can_type_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("CAN类型:")
        label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        can_type_layout.addWidget(label)
        self.can_type_combo = QComboBox()
        self.can_type_combo.setSizeAdjustPolicy(
            QComboBox.AdjustToContents)  # 设置自适应内容
        self.can_type_combo.setMinimumWidth(100)  # 设置最小宽度
        self.can_type_combo.setStyleSheet(
            "QComboBox { min-width: 100px; }")  # 通过样式表设置最小宽度
        self.can_type_combo.currentIndexChanged.connect(
            self._on_can_type_changed)
        can_type_layout.addWidget(self.can_type_combo)
        can_type_widget = QWidget()
        can_type_widget.setLayout(can_type_layout)
        send_layout.addWidget(can_type_widget, 0, 8)

        # 第三行
        # 帧ID组
        frame_id_layout = QHBoxLayout()
        frame_id_layout.setSpacing(2)
        frame_id_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("帧ID(hex):")
        label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        frame_id_layout.addWidget(label)
        self.frame_id_edit = QLineEdit()
        self.frame_id_edit.setText("288")
        frame_id_layout.addWidget(self.frame_id_edit)
        frame_id_widget = QWidget()
        frame_id_widget.setLayout(frame_id_layout)
        send_layout.addWidget(frame_id_widget, 1, 0)

        # 长度组
        frame_len_layout = QHBoxLayout()
        frame_len_layout.setSpacing(2)
        frame_len_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("长度:")
        label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        frame_len_layout.addWidget(label)
        self.frame_len_combo = QComboBox()
        self.frame_len_combo.setSizeAdjustPolicy(
            QComboBox.AdjustToContents)  # 设置自适应内容
        frame_len_layout.addWidget(self.frame_len_combo)
        frame_len_widget = QWidget()
        frame_len_widget.setLayout(frame_len_layout)
        send_layout.addWidget(frame_len_widget, 1, 2)

        # 数据组
        frame_data_layout = QHBoxLayout()
        frame_data_layout.setSpacing(2)
        frame_data_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("数据(hex):")
        label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        frame_data_layout.addWidget(label)
        self.frame_data_edit = QLineEdit()
        self.frame_data_edit.setText("00 00 00 00 40 08 00 00")
        frame_data_layout.addWidget(self.frame_data_edit)
        frame_data_widget = QWidget()
        frame_data_widget.setLayout(frame_data_layout)
        send_layout.addWidget(frame_data_widget, 1, 4, 1, 5)  # 修改为跨5列，使输入框更宽

        # 第四行
        # 发送帧数组
        send_num_layout = QHBoxLayout()
        send_num_layout.setSpacing(2)
        send_num_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("发送帧数:")
        label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        send_num_layout.addWidget(label)
        self.send_num_edit = QLineEdit()
        self.send_num_edit.setText("1")
        send_num_layout.addWidget(self.send_num_edit)
        send_num_widget = QWidget()
        send_num_widget.setLayout(send_num_layout)
        send_layout.addWidget(send_num_widget, 2, 0)

        # 发送次数组
        send_cnt_layout = QHBoxLayout()
        send_cnt_layout.setSpacing(2)
        send_cnt_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("发送次数:")
        label.setFixedWidth(80)  # 增加标签宽度，从65改为80
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        send_cnt_layout.addWidget(label)
        self.send_cnt_edit = QLineEdit()
        self.send_cnt_edit.setText("-1")
        self.send_cnt_edit.setToolTip("-1表示循环发送，大于0的整数表示发送指定次数")
        # 添加输入验证
        self.send_cnt_edit.textChanged.connect(self._validate_send_count)
        send_cnt_layout.addWidget(self.send_cnt_edit)
        send_cnt_widget = QWidget()
        send_cnt_widget.setLayout(send_cnt_layout)
        send_layout.addWidget(send_cnt_widget, 2, 2)

        # 发送间隔组
        send_period_layout = QHBoxLayout()
        send_period_layout.setSpacing(2)
        send_period_layout.setContentsMargins(0, 0, 0, 0)
        label = QLabel("发送间隔(ms):")
        label.setFixedWidth(110)  # 增加标签宽度，从85改为110，因为这个文本较长
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        send_period_layout.addWidget(label)
        self.send_period_edit = QLineEdit()
        self.send_period_edit.setText("40")
        send_period_layout.addWidget(self.send_period_edit)
        send_period_widget = QWidget()
        send_period_widget.setLayout(send_period_layout)
        send_layout.addWidget(send_period_widget, 2, 4)

        # ID递增和发送按钮
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(5)  # 保持按钮之间的间距
        btn_layout.setContentsMargins(0, 0, 0, 0)
        self.send_btn = QPushButton("开始自定义发送")
        self.send_btn.setCheckable(True)  # 设置为可切换按钮
        self.send_btn.clicked.connect(self._on_send_clicked)
        btn_layout.addWidget(self.send_btn)
        btn_widget = QWidget()
        btn_widget.setLayout(btn_layout)
        send_layout.addWidget(btn_widget, 2, 6)

        # 设置列间距
        send_layout.setHorizontalSpacing(30)  # 增加组件组之间的间距
        send_layout.setVerticalSpacing(10)    # 设置垂直间距

        # 设置边距
        send_layout.setContentsMargins(10, 10, 10, 10)

        self.msg_send_group.setLayout(send_layout)
        layout.addWidget(self.msg_send_group)

        self.setLayout(layout)

        # 初始化时禁用所有发送组控件
        self.set_send_group_enabled(False)

    def set_canfd_enabled(self, enabled: bool, protocol_type: int = 0):
        """设置CANFD功能是否启用

        Args:
            enabled: 是否启用
            protocol_type: 协议类型(0:CAN, 1:CANFD)
                         当protocol_type=1时，可以指定是否启用BRS特性
        """
        self.can_type_combo.clear()
        if enabled:
            # 添加选项 - 保留CAN/CANFD/CANFD BRS选项供用户选择
            # 但在内部实现中，CANFD和CANFD BRS都使用相同的协议类型(CANFD_TYPE_CANFD=1)
            # 区别仅在于设置BRS标志位
            self.can_type_combo.addItems(["CAN", "CANFD", "CANFD BRS"])

            if protocol_type == 0:  # CAN协议
                self.can_type_combo.setCurrentText("CAN")
            elif protocol_type == 1:  # CANFD协议
                # 根据UI界面需要，可以支持"CANFD"和"CANFD BRS"两种显示
                # 但在底层API调用时，它们都是CANFD类型，只是BRS标志位不同
                # 此处默认选择"CANFD"，需要启用BRS特性时在发送消息时设置BRS标志位
                self.can_type_combo.setCurrentText("CANFD")
        else:
            # 设备不支持CANFD
            self.can_type_combo.addItems(["CAN"])

        self._update_frame_len_options()

    def set_send_enabled(self, enabled: bool):
        """设置发送功能是否启用

        Args:
            enabled: 是否启用
        """
        self.send_btn.setEnabled(enabled)

    def set_send_group_enabled(self, enabled: bool):
        """设置发送组内所有控件是否启用

        Args:
            enabled: 是否启用
        """
        # 禁用/启用发送按钮
        self.send_btn.setEnabled(enabled)

        # 禁用/启用所有下拉框
        self.channel_combo.setEnabled(enabled)
        self.send_type_combo.setEnabled(enabled)
        self.frame_type_combo.setEnabled(enabled)
        self.frame_format_combo.setEnabled(enabled)
        self.can_type_combo.setEnabled(enabled)
        self.frame_len_combo.setEnabled(enabled)

        # 禁用/启用所有输入框
        self.frame_id_edit.setEnabled(enabled)
        self.frame_data_edit.setEnabled(enabled)
        self.send_num_edit.setEnabled(enabled)
        self.send_cnt_edit.setEnabled(enabled)
        self.send_period_edit.setEnabled(enabled)

    def update_counters(self, tx_cnt: int, rx_cnt: int):
        """更新计数器显示

        Args:
            tx_cnt: 发送计数
            rx_cnt: 接收计数
        """
        with self._counter_lock:
            self._tx_count = tx_cnt
            self._rx_count = rx_cnt
            # 更新UI显示
            QMetaObject.invokeMethod(self.tx_cnt_label,
                                     "setText",
                                     Qt.QueuedConnection,
                                     Q_ARG(str, str(self._tx_count)))
            QMetaObject.invokeMethod(self.rx_cnt_label,
                                     "setText",
                                     Qt.QueuedConnection,
                                     Q_ARG(str, str(self._rx_count)))

    def add_message(self, msg_dict: Dict[str, Any]):
        """添加一条消息到缓冲区

        Args:
            msg_dict: 消息字典
        """
        # 更新计数器和缓冲区，确保原子性
        with self._counter_lock:
            # 先更新发送/接收计数，这个要在过滤前做
            if msg_dict["direction"] == "发送":
                # 检查是否是自发自收模式的额外发送事件
                current_text = self.send_type_combo.currentText()
                if current_text != "自发自收" or not hasattr(self, '_last_send_time') or \
                   time.time() - self._last_send_time > 0.001:  # 1ms阈值
                    self._tx_count += 1
                    if current_text == "自发自收":
                        self._last_send_time = time.time()
            else:
                self._rx_count += 1

            # 序号等于总帧数
            msg_dict["seq"] = self._tx_count + self._rx_count

            # 更新UI显示计数
            QMetaObject.invokeMethod(self.tx_cnt_label,
                                     "setText",
                                     Qt.QueuedConnection,
                                     Q_ARG(str, str(self._tx_count)))
            QMetaObject.invokeMethod(self.rx_cnt_label,
                                     "setText",
                                     Qt.QueuedConnection,
                                     Q_ARG(str, str(self._rx_count)))

            # 再检查过滤条件，决定是否显示
            if self._apply_filter(msg_dict):
                # 添加到显示缓冲区
                if len(self._msg_buffer) >= self._max_buffer_size:
                    self._msg_buffer.pop(0)
                self._msg_buffer.append(msg_dict)

    def _update_display(self):
        """定时更新显示"""
        try:
            # 如果暂停刷新，直接返回
            if self._is_paused:
                return

            # 获取缓冲区中的所有消息
            with self._counter_lock:  # 使用同一个锁确保一致性
                messages = self._msg_buffer.copy()
                self._msg_buffer.clear()

            if not messages:
                return

            # 批量更新UI
            self.msg_tree.setUpdatesEnabled(False)  # 暂停UI更新
            try:
                for msg_dict in messages:
                    # 如果超过最大显示数量，删除最早的消息
                    while self.msg_tree.topLevelItemCount() >= MAX_DISPLAY:
                        self.msg_tree.takeTopLevelItem(0)

                    # 创建新的消息项
                    item = QTreeWidgetItem()
                    item.setText(0, str(msg_dict["seq"]))  # 使用消息中保存的序号
                    item.setText(1, msg_dict.get(
                        "timestamp", datetime.now().strftime("%H:%M:%S.%f")[:-3]))
                    item.setText(
                        2, f"设备{msg_dict['device']} 通道{msg_dict['channel']}")
                    item.setText(3, msg_dict["id"])
                    item.setText(4, msg_dict["direction"])
                    item.setText(5, msg_dict["info"])
                    item.setText(6, str(msg_dict["dlc"]))
                    item.setText(7, msg_dict["data"])

                    self.msg_tree.addTopLevelItem(item)

                # 如果不是暂停状态，滚动到最新消息
                if not self._is_paused:
                    self.msg_tree.scrollToBottom()

            finally:
                self.msg_tree.setUpdatesEnabled(True)  # 恢复UI更新

        except Exception as e:
            logger.error(f"更新显示异常: {e}", exc_info=True)

    def _apply_filter(self, msg_dict: Dict[str, Any]) -> bool:
        """应用过滤器

        Args:
            msg_dict: 消息字典

        Returns:
            bool: 是否通过过滤
        """
        try:
            # 通道过滤
            if self._filter_channel != "全部":
                channel_key = f"设备{msg_dict['device']} 通道{msg_dict['channel']}"
                if channel_key != self._filter_channel:
                    return False

            # ID过滤
            if self._filter_id:
                msg_id = msg_dict["id"].upper()
                filter_id = self._filter_id.upper()
                if filter_id not in msg_id:
                    return False

            # 方向过滤
            if self._filter_direction != "全部" and self._filter_direction != msg_dict["direction"]:
                return False

            return True

        except Exception as e:
            logger.error(f"过滤器应用异常: {e}", exc_info=True)
            return True  # 发生异常时默认显示消息

    def _on_pause_clicked(self, checked: bool):
        """暂停刷新按钮点击处理"""
        self._is_paused = checked  # 更新暂停状态
        self.pause_btn.setText("继续刷新" if checked else "暂停刷新")

    def _on_filter_changed(self):
        """过滤条件改变处理"""
        try:
            # 更新过滤设置
            self._filter_channel = self.channel_filter_combo.currentText()
            self._filter_id = self.id_filter_edit.text().strip()
            self._filter_direction = self.direction_filter_combo.currentText()

            logger.debug(
                f"过滤条件更新: 通道={self._filter_channel}, ID={self._filter_id}, 方向={self._filter_direction}")

            # 重新应用过滤条件
            self._reapply_filters()

        except Exception as e:
            logger.error(f"过滤条件更新异常: {e}", exc_info=True)

    def _on_reset_filter_clicked(self):
        """重置筛选按钮点击处理"""
        # 重置过滤控件状态
        self.channel_filter_combo.setCurrentText("全部")
        self.id_filter_edit.clear()
        self.direction_filter_combo.setCurrentText("全部")

        # 重置过滤器
        self._message_filter.reset_filters()

        # 重新应用过滤条件
        self._reapply_filters()

    def _reapply_filters(self):
        """重新应用过滤条件到现有消息"""
        try:
            # 暂停UI更新
            self.msg_tree.setUpdatesEnabled(False)

            # 保存所有消息
            all_messages = []
            for i in range(self.msg_tree.topLevelItemCount()):
                item = self.msg_tree.topLevelItem(i)
                msg_dict = {
                    "device": int(item.text(2).split()[0][2:]),  # "设备X" -> X
                    "channel": int(item.text(2).split()[1][2:]),  # "通道X" -> X
                    "id": item.text(3),
                    "direction": item.text(4),
                    "info": item.text(5),
                    "dlc": int(item.text(6)),
                    "data": item.text(7),
                    "timestamp": item.text(1)
                }
                all_messages.append(msg_dict)

            # 清空消息列表
            self.msg_tree.clear()

            # 重新添加符合过滤条件的消息
            for msg_dict in all_messages:
                if self._apply_filter(msg_dict):
                    item = QTreeWidgetItem()
                    item.setText(0, str(self.msg_tree.topLevelItemCount() + 1))
                    item.setText(1, msg_dict["timestamp"])
                    item.setText(
                        2, f"设备{msg_dict['device']} 通道{msg_dict['channel']}")
                    item.setText(3, msg_dict["id"])
                    item.setText(4, msg_dict["direction"])
                    item.setText(5, msg_dict["info"])
                    item.setText(6, str(msg_dict["dlc"]))
                    item.setText(7, msg_dict["data"])
                    self.msg_tree.addTopLevelItem(item)

        except Exception as e:
            logger.error(f"重新应用过滤条件异常: {e}", exc_info=True)
        finally:
            # 恢复UI更新
            self.msg_tree.setUpdatesEnabled(True)

    def _on_clear_clicked(self):
        """清空按钮点击处理"""
        # 清空消息列表
        self.msg_tree.clear()

        # 重置内部计数器（使用锁保护）
        with self._counter_lock:
            self._tx_count = 0
            self._rx_count = 0
            self._msg_seq = 0  # 重置消息序号计数器
            # 更新UI显示
            QMetaObject.invokeMethod(self.tx_cnt_label,
                                     "setText",
                                     Qt.QueuedConnection,
                                     Q_ARG(str, "0"))
            QMetaObject.invokeMethod(self.rx_cnt_label,
                                     "setText",
                                     Qt.QueuedConnection,
                                     Q_ARG(str, "0"))

        # 重置所有打开通道的计数器
        for channel_key in self._opened_channels:
            dev_idx, chn_idx = channel_key
            self.reset_counters.emit(dev_idx, chn_idx)

        # 清空消息缓冲区
        with self._counter_lock:
            self._msg_buffer.clear()

    def _on_frame_format_changed(self, index: int):
        """帧格式改变处理

        Args:
            index: 当前选择的索引
        """
        if index == 0:  # 数据帧
            self.frame_data_edit.setEnabled(True)
        else:  # 远程帧
            self.frame_data_edit.setEnabled(False)
            self.can_type_combo.setCurrentIndex(0)  # 远程帧时强制使用 CAN
        self._update_frame_len_options()

    def _on_can_type_changed(self, index: int):
        """CAN类型改变处理

        Args:
            index: 当前选择的索引
        """
        self._update_frame_len_options()

    def _update_frame_len_options(self):
        """更新帧长度选项"""
        self.frame_len_combo.clear()
        if self.frame_format_combo.currentIndex() == 1:  # 远程帧
            self.frame_len_combo.addItems([str(i) for i in range(9)])
        else:  # 数据帧
            if self.can_type_combo.currentIndex() > 0:  # CANFD
                self.frame_len_combo.addItems(
                    [str(self._dlc_to_len(i)) for i in range(16)])
            else:  # 普通CAN
                self.frame_len_combo.addItems([str(i) for i in range(9)])

        # 总是默认选择8
        self.frame_len_combo.setCurrentText("8")

    def _dlc_to_len(self, dlc: int) -> int:
        """DLC转换为实际数据长度"""
        if dlc <= 8:
            return dlc
        elif dlc == 9:
            return 12
        elif dlc == 10:
            return 16
        elif dlc == 11:
            return 20
        elif dlc == 12:
            return 24
        elif dlc == 13:
            return 32
        elif dlc == 14:
            return 48
        else:
            return 64

    def _validate_send_count(self, text: str):
        """验证发送次数输入

        Args:
            text: 输入的文本
        """
        if not text:  # 允许输入框为空
            return

        try:
            count = int(text)
            # 检查是否为-1或大于0的整数
            if count != -1 and count <= 0:
                # 恢复为上一个有效值
                self.send_cnt_edit.setText("1")
                QMessageBox.warning(self, "警告", "发送次数必须为-1（循环发送）或大于0的整数！")
        except ValueError:
            # 输入的不是整数，恢复为上一个有效值
            self.send_cnt_edit.setText("1")
            QMessageBox.warning(self, "警告", "请输入有效的整数！")

    def _on_send_clicked(self, checked: bool):
        """发送按钮点击处理"""
        if checked:  # 开始发送
            try:
                # 从当前选择的通道文本中解析设备和通道索引
                current_text = self.channel_combo.currentText()
                if not current_text:
                    QMessageBox.warning(self, "警告", "请选择要发送的通道!")
                    self.send_btn.setChecked(False)  # 恢复按钮状态
                    return

                # 设备索引固定为0
                dev_idx = 0
                chn_idx = int(current_text.split()[1][2:])  # "通道X" -> X

                # 检查通道是否已打开
                if (dev_idx, chn_idx) not in self._opened_channels:
                    QMessageBox.warning(
                        self, "警告", f"设备{dev_idx}通道{chn_idx}未打开!")
                    self.send_btn.setChecked(False)  # 恢复按钮状态
                    return

                # 获取发送参数
                can_type_index = self.can_type_combo.currentIndex()
                can_type_text = self.can_type_combo.currentText()
                is_canfd = can_type_index > 0
                is_brs = can_type_index == 2  # CANFD BRS

                # 详细记录CAN类型和BRS状态
                logger.info(
                    f"准备发送消息: 选择的CAN类型={can_type_text}, CANFD={is_canfd}, BRS={is_brs}")

                msg_info = {
                    "device": dev_idx,
                    "channel": chn_idx,
                    "is_canfd": is_canfd,
                    "can_type": can_type_text,  # 添加can_type字段记录UI选择
                    "id": int(self.frame_id_edit.text(), 16),
                    "flags": (
                        (0x1 if self.frame_type_combo.currentIndex() == 1 else 0) |  # 扩展帧
                        (0x2 if self.frame_format_combo.currentIndex() == 1 else 0) |  # 远程帧
                        (0x4 if is_brs else 0)  # BRS - 明确根据is_brs设置标志位
                    ),
                    "len": int(self.frame_len_combo.currentText()),
                    "data": [],
                    "send_type": self.send_type_combo.currentText(),
                }

                # 解析数据
                if self.frame_format_combo.currentIndex() == 0:  # 数据帧
                    data_str = self.frame_data_edit.text().strip()
                    if data_str:
                        data_list = data_str.split()
                        if len(data_list) < msg_info["len"]:
                            raise ValueError("数据长度不足")
                        msg_info["data"] = [int(x, 16)
                                            for x in data_list[:msg_info["len"]]]
                    else:
                        msg_info["data"] = [0] * msg_info["len"]

                # 获取发送次数
                send_count = int(self.send_cnt_edit.text())
                send_num = int(self.send_num_edit.text())
                send_period = int(self.send_period_edit.text())

                # 设置按钮文本
                self.send_btn.setText("停止自定义发送")

                # 发送发送状态变更信号
                self.sending_state_changed.emit(True)

                # 开始发送
                self._message_sender.start_sending(
                    msg_info,
                    send_count,
                    send_num,
                    send_period,
                    lambda msg: self._send_message(msg)
                )

            except ValueError as e:
                QMessageBox.warning(self, "警告", str(e))
                self.send_btn.setChecked(False)  # 恢复按钮状态
                self.send_btn.setText("开始自定义发送")
        else:  # 停止发送
            self._message_sender.stop_sending()
            self.send_btn.setText("开始自定义发送")
            # 发送发送状态变更信号
            self.sending_state_changed.emit(False)

    def _send_message(self, msg_info: Dict[str, Any]) -> bool:
        """发送消息（同步底层发送结果）"""
        try:
            # 如果窗口正在关闭，不再发送消息
            if self._is_closing:
                return False
            # 直接调用底层CANController的send_message方法
            dev_idx = 0  # 设备索引始终为0
            chn_idx = msg_info.get("channel", 0)
            return self._can_controller.send_message(dev_idx, chn_idx, msg_info)
        except Exception:
            return False

    def _on_send_error(self, error_msg: str):
        """发送错误处理"""
        # 如果窗口正在关闭，不再显示错误消息
        if self._is_closing:
            return

        logger.error(f"报文发送失败: {error_msg}", exc_info=True)
        self.send_btn.setChecked(False)
        self.send_btn.setText("开始自定义发送")
        # 发送发送状态变更信号
        self.sending_state_changed.emit(False)
        QMessageBox.warning(self, "发送失败", f"报文发送失败: {error_msg}")

    def _on_send_complete(self):
        """发送完成处理"""
        # 如果窗口正在关闭，不再处理完成事件
        if self._is_closing:
            return

        self.send_btn.setChecked(False)
        self.send_btn.setText("开始自定义发送")
        # 发送发送状态变更信号
        self.sending_state_changed.emit(False)

    def stop_all_threads(self):
        """停止所有发送和接收线程"""
        self._message_sender.stop_sending()

    def update_channel_state(self, dev_idx: int, chn_idx: int, is_open: bool):
        """更新通道状态

        Args:
            dev_idx: 设备索引 (始终为0)
            chn_idx: 通道索引
            is_open: 是否打开
        """
        # 强制设备索引为0
        dev_idx = 0
        channel_key = (dev_idx, chn_idx)
        channel_text = f"设备{dev_idx} 通道{chn_idx}"

        if is_open:
            # 获取通道的协议类型
            protocol_type = self._channel_panel.get_protocol_type(chn_idx)
            self._opened_channels[channel_key] = protocol_type

            # 如果通道不在下拉列表中，添加该通道
            if self.channel_combo.findText(channel_text) == -1:
                self.channel_combo.addItem(channel_text)
                self.channel_combo.setCurrentText(channel_text)

            # 添加到过滤通道下拉列表
            if self.channel_filter_combo.findText(channel_text) == -1:
                self.channel_filter_combo.addItem(channel_text)

            # 有通道打开，启用发送组控件
            self.set_send_group_enabled(True)
        else:
            # 停止所有发送线程
            self.stop_all_threads()

            if channel_key in self._opened_channels:
                del self._opened_channels[channel_key]

            # 从下拉列表中移除关闭的通道
            index = self.channel_combo.findText(channel_text)
            if index != -1:
                self.channel_combo.removeItem(index)

            # 从过滤通道下拉列表中移除
            index = self.channel_filter_combo.findText(channel_text)
            if index != -1:
                self.channel_filter_combo.removeItem(index)

        # 如果当前选中的通道被关闭，切换到第一个打开的通道
        current_text = self.channel_combo.currentText()
        if not is_open and current_text == channel_text:
            first_channel = self._channel_manager.get_first_opened_channel()
            if first_channel:
                dev_idx, chn_idx = first_channel
                self.channel_combo.setCurrentText(f"设备{dev_idx} 通道{chn_idx}")

        # 检查是否所有通道都关闭了
        if len(self._opened_channels) == 0:
            # 所有通道都关闭，禁用发送组控件
            self.set_send_group_enabled(False)

    def on_protocol_changed(self, channel: int, protocol_type: int):
        """处理通道协议变化

        Args:
            channel: 通道索引
            protocol_type: 协议类型(0:CAN, 1:CANFD)
                         当protocol_type=1时，可以指定是否启用BRS特性
        """
        # 检查是否是当前选中的通道
        current_text = self.channel_combo.currentText()
        if not current_text:
            return

        current_channel = int(current_text.split()[1][2:])  # "通道X" -> X
        if current_channel == channel:
            # 更新CAN类型选项 - 这里传递原始的protocol_type以保持UI一致性
            # 但在实际调用API时，CANFD和CANFD BRS都使用CANFD_TYPE_CANFD(1)
            self.set_canfd_enabled(protocol_type > 0, protocol_type)
        else:
            # 如果通道不是当前选中的通道，忽略
            return

    def _on_channel_changed(self, text: str):
        """通道切换处理

        Args:
            text: 当前选择的通道文本
        """
        if not text:
            return

        try:
            # 解析设备和通道索引
            # 设备索引固定为0
            dev_idx = 0
            chn_idx = int(text.split()[1][2:])  # "通道X" -> X

            # 获取当前通道的协议类型
            channel_key = (dev_idx, chn_idx)
            if channel_key in self._opened_channels:
                # 更新CAN类型选项
                protocol_type = self._opened_channels[channel_key]
                # 根据通道的实际协议类型设置CAN类型选项
                self.set_canfd_enabled(protocol_type > 0, protocol_type)
        except Exception as e:
            print(f"通道切换处理异常: {e}")

    def _on_column_resized(self, logical_index: int, old_size: int, new_size: int):
        """列宽改变处理"""
        if logical_index in self.COLUMN_NAMES:
            column_name = self.COLUMN_NAMES[logical_index]
            # 更新配置中的列宽
            current_widths = self._config.get_value(
                ConfigType.MESSAGE_PANEL, "列宽", {})
            current_widths[column_name] = new_size
            self._config.set_value(
                ConfigType.MESSAGE_PANEL, "列宽", current_widths)

    def _restore_column_widths(self):
        """恢复保存的列宽"""
        # 获取默认列宽
        default_widths = self._get_default_column_widths()

        # 从配置中获取保存的列宽
        saved_widths = self._config.get_value(
            ConfigType.MESSAGE_PANEL, "列宽", {})

        # 恢复每列的宽度
        for i, default_width in enumerate(default_widths):
            if i == len(default_widths) - 1:  # 跳过最后一列（数据列）
                continue
            # 使用保存的宽度，如果没有则使用默认值
            column_name = self.COLUMN_NAMES[i]
            width = saved_widths.get(column_name, default_width)
            if width > 0:  # 只设置固定宽度的列
                self.msg_tree.setColumnWidth(i, width)

    def _get_default_column_widths(self) -> list[int]:
        """获取默认列宽

        Returns:
            list[int]: 默认列宽列表
        """
        return [
            MSGCNT_WIDTH,    # 序号列
            100,             # 时间列
            MSGCHN_WIDTH,    # 通道列
            MSGID_WIDTH,     # 帧ID列
            MSGDIR_WIDTH,    # 方向列
            MSGINFO_WIDTH,   # 帧信息列
            MSGLEN_WIDTH,    # 长度列
            -1              # 数据列（自动调整）
        ]

    def _on_send_type_changed(self, text: str):
        """发送方式改变处理

        Args:
            text: 当前选择的发送方式
        """
        # 已删除ID递增相关代码
        pass

    def _on_message_added(self, msg_dict: Dict[str, Any]):
        """消息添加处理"""
        # 检查是否需要清除旧消息
        if self.msg_tree.topLevelItemCount() >= MAX_DISPLAY:
            self.msg_tree.takeTopLevelItem(0)

        # 强制设备索引为0
        msg_dict['device'] = 0

        # 添加新消息
        item = QTreeWidgetItem()
        item.setText(0, str(self.msg_tree.topLevelItemCount()))
        item.setText(1, msg_dict["timestamp"])
        item.setText(2, f"设备{msg_dict['device']} 通道{msg_dict['channel']}")
        item.setText(3, msg_dict["id"])
        item.setText(4, msg_dict["direction"])
        item.setText(5, msg_dict["info"])
        item.setText(6, str(msg_dict["dlc"]))
        item.setText(7, msg_dict["data"])

        self.msg_tree.addTopLevelItem(item)
        self.msg_tree.scrollToItem(item)

    def _on_counters_updated(self, tx_cnt: int, rx_cnt: int):
        """计数器更新处理"""
        self.tx_cnt_label.setText(str(tx_cnt))
        self.rx_cnt_label.setText(str(rx_cnt))

    def _on_counters_reset(self):
        """计数器重置处理"""
        self.tx_cnt_label.setText("0")
        self.rx_cnt_label.setText("0")

    def _on_channel_state_changed(self, dev_idx: int, chn_idx: int, is_open: bool):
        """通道状态改变处理"""
        # 强制设备索引为0
        dev_idx = 0
        channel_text = f"设备{dev_idx} 通道{chn_idx}"

        if is_open:
            # 如果通道不在下拉列表中，添加该通道
            if self.channel_combo.findText(channel_text) == -1:
                self.channel_combo.addItem(channel_text)
                self.channel_combo.setCurrentText(channel_text)

            # 添加到过滤通道下拉列表
            if self.channel_filter_combo.findText(channel_text) == -1:
                self.channel_filter_combo.addItem(channel_text)
        else:
            # 停止所有发送线程
            self._message_sender.stop_sending()

            # 从下拉列表中移除关闭的通道
            index = self.channel_combo.findText(channel_text)
            if index != -1:
                self.channel_combo.removeItem(index)

            # 从过滤通道下拉列表中移除
            index = self.channel_filter_combo.findText(channel_text)
            if index != -1:
                self.channel_filter_combo.removeItem(index)

        # 如果当前选中的通道被关闭，切换到第一个打开的通道
        current_text = self.channel_combo.currentText()
        if not is_open and current_text == channel_text:
            first_channel = self._channel_manager.get_first_opened_channel()
            if first_channel:
                dev_idx, chn_idx = first_channel
                self.channel_combo.setCurrentText(f"设备{dev_idx} 通道{chn_idx}")
            else:
                self.channel_combo.clear()
                self.channel_filter_combo.setCurrentText("全部")

    def set_send_error(self, is_error: bool):
        """设置发送错误状态

        Args:
            is_error: 是否发送错误
        """
        if is_error:
            self._on_send_error("发送失败")
        else:
            self.send_btn.setChecked(False)
            self.send_btn.setText("开始自定义发送")

    def get_column_widths(self) -> dict:
        """获取列宽设置"""
        result = {}
        for col_idx in range(self.msg_tree.columnCount()):
            col_width = self.msg_tree.columnWidth(col_idx)
            result[self.COLUMN_NAMES.get(col_idx, str(col_idx))] = col_width
        return result

    def on_message_sent(self, success, message_info=None):
        """接收消息发送状态反馈"""
        if success:
            pass
        else:
            logger.error("消息发送失败")
            if message_info:
                logger.error(f"失败消息信息: {message_info}")
