"""
设备信息对话框
"""
from typing import Dict, Any, Optional
import os
import platform
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                             QGridLayout, QGroupBox, QTextEdit, QTabWidget, QWidget, QScrollArea)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from src.utils.paths import API_DIR


class DeviceInfoDialog(QDialog):
    """设备信息对话框类"""

    def __init__(self, parent=None):
        """初始化设备信息对话框

        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.setWindowTitle("当前设备信息")
        self.setMinimumSize(600, 500)

        # 初始化UI
        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()

        # 设备基本信息组
        self.info_group = QGroupBox("设备基本信息")
        self.info_group.setFont(QFont("", -1, QFont.Bold))

        # 使用滚动区域包装信息内容
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        info_layout = QGridLayout(scroll_content)
        info_layout.setVerticalSpacing(15)
        info_layout.setHorizontalSpacing(10)
        info_layout.setContentsMargins(10, 20, 10, 20)

        # 设备类型
        dev_type_label = QLabel("设备类型:")
        dev_type_label.setMinimumWidth(120)
        dev_type_label.setFixedHeight(30)
        info_layout.addWidget(dev_type_label, 0, 0)
        self.dev_type_value = QLabel()
        self.dev_type_value.setFixedHeight(30)
        self.dev_type_value.setWordWrap(True)
        info_layout.addWidget(self.dev_type_value, 0, 1)

        # 硬件版本
        hw_ver_label = QLabel("硬件版本:")
        hw_ver_label.setMinimumWidth(120)
        hw_ver_label.setFixedHeight(30)
        info_layout.addWidget(hw_ver_label, 1, 0)
        self.hw_ver_value = QLabel()
        self.hw_ver_value.setFixedHeight(30)
        self.hw_ver_value.setWordWrap(True)
        info_layout.addWidget(self.hw_ver_value, 1, 1)

        # 固件版本
        fw_ver_label = QLabel("固件版本:")
        fw_ver_label.setMinimumWidth(120)
        fw_ver_label.setFixedHeight(30)
        info_layout.addWidget(fw_ver_label, 2, 0)
        self.fw_ver_value = QLabel()
        self.fw_ver_value.setFixedHeight(30)
        self.fw_ver_value.setWordWrap(True)
        info_layout.addWidget(self.fw_ver_value, 2, 1)

        # 驱动接口版本（PC端驱动程序版本）
        dr_ver_label = QLabel("驱动接口版本:")
        dr_ver_label.setMinimumWidth(120)
        dr_ver_label.setFixedHeight(30)
        info_layout.addWidget(dr_ver_label, 3, 0)
        self.dr_ver_value = QLabel()
        self.dr_ver_value.setFixedHeight(30)
        self.dr_ver_value.setWordWrap(True)
        info_layout.addWidget(self.dr_ver_value, 3, 1)

        # 接口版本（设备固件协议/命令集版本）
        in_ver_label = QLabel("接口版本:")
        in_ver_label.setMinimumWidth(120)
        in_ver_label.setFixedHeight(30)
        info_layout.addWidget(in_ver_label, 4, 0)
        self.in_ver_value = QLabel()
        self.in_ver_value.setFixedHeight(30)
        self.in_ver_value.setWordWrap(True)
        info_layout.addWidget(self.in_ver_value, 4, 1)

        # 中断号
        irq_num_label = QLabel("中断号:")
        irq_num_label.setMinimumWidth(120)
        irq_num_label.setFixedHeight(30)
        info_layout.addWidget(irq_num_label, 5, 0)
        self.irq_num_value = QLabel()
        self.irq_num_value.setFixedHeight(30)
        info_layout.addWidget(self.irq_num_value, 5, 1)

        # CAN通道数
        can_num_label = QLabel("CAN通道数:")
        can_num_label.setMinimumWidth(120)
        can_num_label.setFixedHeight(30)
        info_layout.addWidget(can_num_label, 6, 0)
        self.can_num_value = QLabel()
        self.can_num_value.setFixedHeight(30)
        info_layout.addWidget(self.can_num_value, 6, 1)

        # 设备序列号
        serial_label = QLabel("设备序列号:")
        serial_label.setMinimumWidth(120)
        serial_label.setFixedHeight(30)
        info_layout.addWidget(serial_label, 7, 0)
        self.serial_value = QLabel()
        self.serial_value.setFixedHeight(30)
        self.serial_value.setWordWrap(True)
        info_layout.addWidget(self.serial_value, 7, 1)

        # 硬件类型
        hw_type_label = QLabel("硬件类型:")
        hw_type_label.setMinimumWidth(120)
        hw_type_label.setFixedHeight(30)
        info_layout.addWidget(hw_type_label, 8, 0)
        self.hw_type_value = QLabel()
        self.hw_type_value.setFixedHeight(30)
        self.hw_type_value.setWordWrap(True)
        info_layout.addWidget(self.hw_type_value, 8, 1)

        # 系统架构信息
        arch_label = QLabel("系统架构:")
        arch_label.setMinimumWidth(120)
        arch_label.setFixedHeight(30)
        info_layout.addWidget(arch_label, 9, 0)
        
        arch = platform.architecture()[0]
        arch_desc = "64位" if arch == '64bit' else "32位"
        self.arch_value = QLabel(arch_desc)
        self.arch_value.setFixedHeight(30)
        info_layout.addWidget(self.arch_value, 9, 1)

        # 设置列宽度
        info_layout.setColumnMinimumWidth(0, 120)  # 第一列最小宽度
        info_layout.setColumnStretch(1, 1)  # 第二列可扩展

        scroll_area.setWidget(scroll_content)

        # 添加滚动区域到基本信息组
        info_group_layout = QVBoxLayout()
        info_group_layout.addWidget(scroll_area)
        self.info_group.setLayout(info_group_layout)
        layout.addWidget(self.info_group)

        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch(1)

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def set_device_info(self, dev_info: Optional[Any], dev_type_name: str):
        """设置设备信息

        Args:
            dev_info: 设备信息对象（需兼容zlgcan.py的ZCAN_DEVICE_INFO属性）
            dev_type_name: 设备类型名称
        """
        if dev_info:
            # 设置设备基本信息
            self.dev_type_value.setText(dev_type_name)
            self.hw_ver_value.setText(dev_info.hw_version)
            self.fw_ver_value.setText(dev_info.fw_version)
            # 驱动接口版本（PC端驱动程序版本）
            self.dr_ver_value.setText(dev_info.dr_version)
            # 接口版本（设备固件协议/命令集版本）
            self.in_ver_value.setText(dev_info.in_version)
            self.irq_num_value.setText(str(dev_info.irq_num))
            self.can_num_value.setText(str(dev_info.can_num))
            self.serial_value.setText(dev_info.serial)
            self.hw_type_value.setText(dev_info.hw_type)

            # 设置工具提示
            self.dev_type_value.setToolTip(dev_type_name)
            self.hw_ver_value.setToolTip(dev_info.hw_version)
            self.fw_ver_value.setToolTip(dev_info.fw_version)
            self.dr_ver_value.setToolTip(dev_info.dr_version)
            self.in_ver_value.setToolTip(dev_info.in_version)
            self.serial_value.setToolTip(dev_info.serial)
            self.hw_type_value.setToolTip(dev_info.hw_type)
        else:
            # 设备未连接时的显示
            self.dev_type_value.setText("设备未连接")
            self.hw_ver_value.setText("设备未连接")
            self.fw_ver_value.setText("设备未连接")
            self.dr_ver_value.setText("设备未连接")
            self.in_ver_value.setText("设备未连接")
            self.irq_num_value.setText("设备未连接")
            self.can_num_value.setText("设备未连接")
            self.serial_value.setText("设备未连接")
            self.hw_type_value.setText("设备未连接")

            # 清除工具提示
            self.dev_type_value.setToolTip("")
            self.hw_ver_value.setToolTip("")
            self.fw_ver_value.setToolTip("")
            self.dr_ver_value.setToolTip("")
            self.in_ver_value.setToolTip("")
            self.serial_value.setToolTip("")
            self.hw_type_value.setToolTip("")

    def set_channel_info(self, channel_info_text: str):
        """设置通道信息

        Args:
            channel_info_text: 通道信息文本
        """
        self.channel_info.setText(channel_info_text)
