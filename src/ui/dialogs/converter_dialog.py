"""
进制转换对话框
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                             QGridLayout, QGroupBox, QLineEdit, QWidget, QApplication,
                             QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtGui import QFont, QValidator, QRegExpValidator
from PyQt5.QtCore import QRegExp

from src.utils.logger import get_logger

# 创建日志记录器
logger = get_logger("converter_dialog")


class ConverterDialog(QDialog):
    """进制转换对话框类"""

    def __init__(self, parent=None):
        """初始化进制转换对话框

        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.setWindowTitle("进制转换")
        self.setMinimumSize(500, 350)  # 增加一点高度以适应新控件

        # 初始化模式标志
        self.ascii_mode = "single"  # 默认为单字符模式

        # 初始化UI
        self._init_ui()

        # 连接信号
        self._connect_signals()

        logger.info("进制转换对话框已创建")

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setSpacing(15)

        # ASCII模式选择
        mode_group = QGroupBox("ASCII处理模式")
        mode_group.setFont(QFont("", -1, QFont.Bold))
        mode_layout = QHBoxLayout()

        self.single_mode_rb = QRadioButton("单字符模式")
        self.single_mode_rb.setChecked(True)  # 默认选中
        self.single_mode_rb.clicked.connect(self._on_mode_changed)
        mode_layout.addWidget(self.single_mode_rb)

        self.string_mode_rb = QRadioButton("字符串模式")
        self.string_mode_rb.clicked.connect(self._on_mode_changed)
        mode_layout.addWidget(self.string_mode_rb)

        # 创建按钮组
        self.mode_button_group = QButtonGroup()
        self.mode_button_group.addButton(self.single_mode_rb, 1)
        self.mode_button_group.addButton(self.string_mode_rb, 2)

        mode_layout.addStretch(1)  # 添加弹性空间
        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)

        # 创建转换组
        self.converter_group = QGroupBox("格式转换")
        self.converter_group.setFont(QFont("", -1, QFont.Bold))
        converter_layout = QGridLayout()
        converter_layout.setVerticalSpacing(15)
        converter_layout.setHorizontalSpacing(10)
        converter_layout.setContentsMargins(15, 20, 15, 20)

        # 二进制输入
        binary_label = QLabel("二进制:")
        binary_label.setMinimumWidth(70)
        converter_layout.addWidget(binary_label, 0, 0)

        self.binary_input = QLineEdit()
        self.binary_input.setPlaceholderText("请输入二进制值")
        # 设置二进制输入验证器，只允许输入0和1
        binary_validator = QRegExpValidator(QRegExp("^[0-1]*$"))
        self.binary_input.setValidator(binary_validator)
        converter_layout.addWidget(self.binary_input, 0, 1)

        # 十进制输入
        decimal_label = QLabel("十进制:")
        decimal_label.setMinimumWidth(70)
        converter_layout.addWidget(decimal_label, 1, 0)

        self.decimal_input = QLineEdit()
        self.decimal_input.setPlaceholderText("请输入十进制值")
        # 设置十进制输入验证器，只允许输入数字
        decimal_validator = QRegExpValidator(QRegExp("^[0-9]*$"))
        self.decimal_input.setValidator(decimal_validator)
        converter_layout.addWidget(self.decimal_input, 1, 1)

        # 十六进制输入
        hex_label = QLabel("十六进制:")
        hex_label.setMinimumWidth(70)
        converter_layout.addWidget(hex_label, 2, 0)

        self.hex_input = QLineEdit()
        self.hex_input.setPlaceholderText("请输入十六进制值")
        # 设置十六进制输入验证器，只允许输入0-9和A-F
        hex_validator = QRegExpValidator(QRegExp("^[0-9A-Fa-f]*$"))
        self.hex_input.setValidator(hex_validator)
        converter_layout.addWidget(self.hex_input, 2, 1)

        # ASCII输入
        ascii_label = QLabel("ASCII:")
        ascii_label.setMinimumWidth(70)
        converter_layout.addWidget(ascii_label, 3, 0)

        self.ascii_input = QLineEdit()
        self.ascii_input.setPlaceholderText("请输入ASCII字符")
        converter_layout.addWidget(self.ascii_input, 3, 1)

        # 设置布局
        self.converter_group.setLayout(converter_layout)
        layout.addWidget(self.converter_group)

        # 说明文本
        hint_label = QLabel("提示: 在任一输入框中输入值后，其他输入框将自动显示对应的转换结果\n"
                            "单字符模式: ASCII和数值直接转换，适用于单个字符\n"
                            "字符串模式: 将ASCII字符串转换为对应的十六进制/十进制/二进制表示")
        hint_label.setStyleSheet("color: gray;")
        hint_label.setWordWrap(True)
        layout.addWidget(hint_label)

        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch(1)

        # 清空按钮
        clear_btn = QPushButton("清空")
        clear_btn.clicked.connect(self._clear_all)
        button_layout.addWidget(clear_btn)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def _connect_signals(self):
        """连接信号槽"""
        # 当用户在输入框中输入内容时，自动进行转换
        self.binary_input.textChanged.connect(self._on_binary_changed)
        self.decimal_input.textChanged.connect(self._on_decimal_changed)
        self.hex_input.textChanged.connect(self._on_hex_changed)
        self.ascii_input.textChanged.connect(self._on_ascii_changed)

    def _on_mode_changed(self):
        """处理模式变更"""
        if self.single_mode_rb.isChecked():
            self.ascii_mode = "single"
            logger.info("切换到单字符模式")
        else:
            self.ascii_mode = "string"
            logger.info("切换到字符串模式")

        # 清空所有输入框
        self._clear_all()

    def _clear_all(self):
        """清空所有输入框"""
        # 暂时断开信号连接，避免循环触发
        self.binary_input.textChanged.disconnect()
        self.decimal_input.textChanged.disconnect()
        self.hex_input.textChanged.disconnect()
        self.ascii_input.textChanged.disconnect()

        # 清空所有输入框
        self.binary_input.clear()
        self.decimal_input.clear()
        self.hex_input.clear()
        self.ascii_input.clear()

        # 重新连接信号
        self.binary_input.textChanged.connect(self._on_binary_changed)
        self.decimal_input.textChanged.connect(self._on_decimal_changed)
        self.hex_input.textChanged.connect(self._on_hex_changed)
        self.ascii_input.textChanged.connect(self._on_ascii_changed)

        logger.info("清空所有输入框")

    def _on_binary_changed(self, text):
        """二进制输入变化处理

        Args:
            text: 输入的文本
        """
        if not text:
            return

        try:
            # 二进制转换为整数
            decimal_value = int(text, 2)

            # 断开其他输入框的信号，避免循环触发
            self.decimal_input.textChanged.disconnect()
            self.hex_input.textChanged.disconnect()
            self.ascii_input.textChanged.disconnect()

            # 更新其他输入框
            self.decimal_input.setText(str(decimal_value))
            self.hex_input.setText(hex(decimal_value)[2:].upper())

            # 单字符模式下，只有当值在可打印ASCII范围内才显示ASCII
            if self.ascii_mode == "single" and 32 <= decimal_value <= 126:
                self.ascii_input.setText(chr(decimal_value))
            else:
                self.ascii_input.setText("")

            # 重新连接信号
            self.decimal_input.textChanged.connect(self._on_decimal_changed)
            self.hex_input.textChanged.connect(self._on_hex_changed)
            self.ascii_input.textChanged.connect(self._on_ascii_changed)

            logger.debug(f"二进制转换: {text} -> {decimal_value}")
        except Exception as e:
            logger.error(f"二进制转换错误: {e}")

    def _on_decimal_changed(self, text):
        """十进制输入变化处理

        Args:
            text: 输入的文本
        """
        if not text:
            return

        try:
            # 十进制字符串转换为整数
            decimal_value = int(text)

            # 断开其他输入框的信号，避免循环触发
            self.binary_input.textChanged.disconnect()
            self.hex_input.textChanged.disconnect()
            self.ascii_input.textChanged.disconnect()

            # 更新其他输入框
            self.binary_input.setText(bin(decimal_value)[2:])
            self.hex_input.setText(hex(decimal_value)[2:].upper())

            # 单字符模式下，只有当值在可打印ASCII范围内才显示ASCII
            if self.ascii_mode == "single" and 32 <= decimal_value <= 126:
                self.ascii_input.setText(chr(decimal_value))
            else:
                self.ascii_input.setText("")

            # 重新连接信号
            self.binary_input.textChanged.connect(self._on_binary_changed)
            self.hex_input.textChanged.connect(self._on_hex_changed)
            self.ascii_input.textChanged.connect(self._on_ascii_changed)

            logger.debug(f"十进制转换: {text} -> {bin(decimal_value)[2:]}")
        except Exception as e:
            logger.error(f"十进制转换错误: {e}")

    def _on_hex_changed(self, text):
        """十六进制输入变化处理

        Args:
            text: 输入的文本
        """
        if not text:
            return

        try:
            # 十六进制字符串转换为整数
            decimal_value = int(text, 16)

            # 断开其他输入框的信号，避免循环触发
            self.binary_input.textChanged.disconnect()
            self.decimal_input.textChanged.disconnect()
            self.ascii_input.textChanged.disconnect()

            # 更新其他输入框
            self.binary_input.setText(bin(decimal_value)[2:])
            self.decimal_input.setText(str(decimal_value))

            # 单字符模式下，只有当值在可打印ASCII范围内才显示ASCII
            if self.ascii_mode == "single" and 32 <= decimal_value <= 126:
                self.ascii_input.setText(chr(decimal_value))
            else:
                self.ascii_input.setText("")

            # 重新连接信号
            self.binary_input.textChanged.connect(self._on_binary_changed)
            self.decimal_input.textChanged.connect(self._on_decimal_changed)
            self.ascii_input.textChanged.connect(self._on_ascii_changed)

            logger.debug(f"十六进制转换: {text} -> {decimal_value}")
        except Exception as e:
            logger.error(f"十六进制转换错误: {e}")

    def _on_ascii_changed(self, text):
        """ASCII输入变化处理

        Args:
            text: 输入的文本
        """
        if not text:
            return

        try:
            # 断开其他输入框的信号，避免循环触发
            self.binary_input.textChanged.disconnect()
            self.decimal_input.textChanged.disconnect()
            self.hex_input.textChanged.disconnect()

            if self.ascii_mode == "single":
                # 单字符模式：只处理最后一个字符
                last_char = text[-1]
                ascii_value = ord(last_char)

                # 更新其他输入框
                self.binary_input.setText(bin(ascii_value)[2:])
                self.decimal_input.setText(str(ascii_value))
                self.hex_input.setText(hex(ascii_value)[2:].upper())

                # 如果输入的不只一个字符，则保留最后一个字符
                if len(text) > 1:
                    self.ascii_input.setText(last_char)

                logger.debug(f"ASCII单字符转换: {last_char} -> {ascii_value}")
            else:
                # 字符串模式：处理整个字符串
                # 生成十六进制字符串
                hex_values = ''.join(
                    [hex(ord(c))[2:].upper().zfill(2) for c in text])
                self.hex_input.setText(hex_values)

                # 对于长字符串，二进制和十进制可能不太有意义，清空它们
                self.binary_input.clear()
                self.decimal_input.clear()

                logger.debug(f"ASCII字符串转换: '{text}' -> 0x{hex_values}")

            # 重新连接信号
            self.binary_input.textChanged.connect(self._on_binary_changed)
            self.decimal_input.textChanged.connect(self._on_decimal_changed)
            self.hex_input.textChanged.connect(self._on_hex_changed)

        except Exception as e:
            logger.error(f"ASCII转换错误: {e}")


# 用于外部调用时打开对话框
def run_converter():
    logger = get_logger("converter_dialog")
    logger.info("通过外部调用启动进制转换对话框")
    # 使用现有的QApplication实例
    app = QApplication.instance()
    dialog = ConverterDialog()
    dialog.exec_()
    return dialog
