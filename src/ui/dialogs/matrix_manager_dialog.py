"""
矩阵管理对话框
用于导入、管理和选择Excel矩阵文件
"""
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QListWidget, QLabel, QFileDialog, QMessageBox,
                             QGroupBox, QSplitter, QCheckBox, QListWidgetItem,
                             QWidget)
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtGui import QIcon

from src.core.matrix_manager import MatrixManager, MatrixError, MatrixSecurityError, MatrixFormatError
from src.utils.logger import get_logger
from src.ui.utils.ui_styles import UIStyles

logger = get_logger("matrix_manager")


class MatrixManagerDialog(QDialog):
    """矩阵管理对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Excel矩阵文件管理")
        self.setMinimumSize(500, 500)

        # 获取矩阵管理器实例
        self.matrix_manager = MatrixManager.get_instance()

        # 初始化UI
        self._init_ui()

        # 加载矩阵列表
        self._load_excel_files()

    def _init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout()

        # 顶部操作按钮区
        top_btn_layout = QHBoxLayout()
        import_btn = QPushButton("导入矩阵(支持多选)")
        import_btn.clicked.connect(self._on_import_clicked)
        top_btn_layout.addWidget(import_btn)
        delete_btn = QPushButton("删除选中的矩阵")
        delete_btn.clicked.connect(self._on_delete_selected_clicked)
        top_btn_layout.addWidget(delete_btn)
        clear_all_btn = QPushButton("清空所有矩阵")
        clear_all_btn.clicked.connect(self._on_clear_all_clicked)
        top_btn_layout.addWidget(clear_all_btn)
        top_btn_layout.addStretch()
        main_layout.addLayout(top_btn_layout)
        # 内容区
        content_layout = QVBoxLayout()

        # 在"当前使用的矩阵"区域，显示通道映射关系
        current_files_group = QGroupBox("当前使用的矩阵")
        current_files_layout = QVBoxLayout()

        # 设置标题字体为加粗
        UIStyles.set_group_title_bold(current_files_group)

        # 添加通道0 (ICAN) 映射显示
        self.channel0_label = QLabel("通道0 (ICAN): 未选择")
        self.channel0_label.setWordWrap(True)
        current_files_layout.addWidget(self.channel0_label)

        # 添加通道1 (ISCAN) 映射显示
        self.channel1_label = QLabel("通道1 (ISCAN): 未选择")
        self.channel1_label.setWordWrap(True)
        current_files_layout.addWidget(self.channel1_label)

        current_files_group.setLayout(current_files_layout)
        content_layout.addWidget(current_files_group)

        files_group = QGroupBox("所有可用的矩阵")
        # 设置标题字体为加粗
        UIStyles.set_group_title_bold(files_group)
        files_layout = QVBoxLayout()
        self.file_list = QListWidget()
        self.file_list.setMinimumWidth(400)
        self.file_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.file_list.itemChanged.connect(self._on_file_selection_changed)
        files_layout.addWidget(self.file_list)
        files_group.setLayout(files_layout)
        content_layout.addWidget(files_group)
        main_layout.addLayout(content_layout)
        # 移除底部应用/取消应用按钮
        self.setLayout(main_layout)

    def _load_excel_files(self):
        """加载所有矩阵到列表中"""
        try:
            # 先同步当前使用的文件状态
            self.matrix_manager.sync_current_matrix_with_files()

            # 清空列表
            self.file_list.clear()

            # 获取当前使用的矩阵文件
            current_files = [
                file for file in self.matrix_manager.get_current_matrix().values() if file]

            # 获取矩阵目录中的所有矩阵
            matrix_dir = self.matrix_manager._matrix_dir
            all_files = []

            if os.path.exists(matrix_dir):
                all_files = [f for f in os.listdir(
                    matrix_dir) if f.lower().endswith('.xlsx')]

            # 添加到列表中
            for file in all_files:
                item = QListWidgetItem(file)
                item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                # 如果是当前使用的文件，则默认选中
                if file in current_files:
                    item.setCheckState(Qt.Checked)
                else:
                    item.setCheckState(Qt.Unchecked)
                self.file_list.addItem(item)

            # 更新当前文件显示
            self._update_current_files_label()

        except Exception as e:
            logger.error(f"加载矩阵列表失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载矩阵列表失败: {str(e)}")

    def _update_current_files_label(self):
        """更新当前使用的矩阵标签，显示通道映射关系"""
        try:
            # 获取当前矩阵文件
            current_matrix = self.matrix_manager.get_current_matrix()

            # 更新通道0 (ICAN) 标签
            ican_file = current_matrix.get('ICAN', '')
            if ican_file:
                self.channel0_label.setText(f"通道0: \t{ican_file}")
            else:
                self.channel0_label.setText("通道0: \t未选择")

            # 更新通道1 (ISCAN) 标签
            iscan_file = current_matrix.get('ISCAN', '')
            if iscan_file:
                self.channel1_label.setText(f"通道1: \t{iscan_file}")
            else:
                self.channel1_label.setText("通道1: \t未选择")

        except Exception as e:
            logger.error(f"更新当前文件标签失败: {str(e)}")
            self.channel0_label.setText("通道0: \t未选择")
            self.channel1_label.setText("通道1: \t未选择")

    def _on_file_selection_changed(self, item):
        """文件列表选择变更处理，勾选即应用，取消即取消应用，且每类互斥"""
        # 统计当前所有被勾选的文件
        selected_ican = None
        selected_iscan = None
        for i in range(self.file_list.count()):
            it = self.file_list.item(i)
            filename = it.text()
            if it.checkState() == Qt.Checked:
                if 'ICAN' in filename:
                    if selected_ican is not None:
                        # 只允许一个ICAN，自动取消上一个
                        it.setCheckState(Qt.Unchecked)
                        continue
                    selected_ican = filename
                elif 'ISCAN' in filename:
                    if selected_iscan is not None:
                        # 只允许一个ISCAN，自动取消上一个
                        it.setCheckState(Qt.Unchecked)
                        continue
                    selected_iscan = filename
        # 校验车型名一致性

        def extract_model_name(filename: str) -> str:
            base = filename.split('.')[0]
            if '_' in base:
                return base.split('_')[0]
            elif '-' in base:
                return base.split('-')[0]
            return base
        if selected_ican and selected_iscan:
            model_ican = extract_model_name(selected_ican)
            model_iscan = extract_model_name(selected_iscan)
            if model_ican != model_iscan:
                # 车型不一致，撤销本次勾选
                if item.text() == selected_ican:
                    item.setCheckState(Qt.Unchecked)
                    selected_ican = None
                elif item.text() == selected_iscan:
                    item.setCheckState(Qt.Unchecked)
                    selected_iscan = None
                QMessageBox.warning(self, "车型不一致",
                                    f"所选ICAN和ISCAN文件不属于同一车型！\nICAN车型: {model_ican}\nISCAN车型: {model_iscan}\n请重新选择。")
        self.matrix_manager.load_specified_files(selected_ican, selected_iscan)
        self._update_current_files_label()

    def _on_apply_selection_clicked(self):
        """应用选中的矩阵按钮点击处理（直接应用，无需弹窗确认）"""
        try:
            selected_files = []
            for i in range(self.file_list.count()):
                item = self.file_list.item(i)
                if item.checkState() == Qt.Checked:
                    selected_files.append(item.text())
            if not selected_files:
                QMessageBox.warning(self, "警告", "请至少选择一个矩阵！")
                return
            ican_file = None
            iscan_file = None
            for file in selected_files:
                if 'ICAN' in file:
                    ican_file = file
                elif 'ISCAN' in file:
                    iscan_file = file
            # 校验车型名是否一致

            def extract_model_name(filename: str) -> str:
                base = filename.split('.')[0]
                if '_' in base:
                    return base.split('_')[0]
                elif '-' in base:
                    return base.split('-')[0]
                return base
            if ican_file and iscan_file:
                model_ican = extract_model_name(ican_file)
                model_iscan = extract_model_name(iscan_file)
                if model_ican != model_iscan:
                    QMessageBox.warning(self, "车型不一致",
                                        f"所选ICAN和ISCAN文件不属于同一车型！\nICAN车型: {model_ican}\nISCAN车型: {model_iscan}\n请重新选择。")
                    return
            self.matrix_manager.load_specified_files(ican_file, iscan_file)
            self._update_current_files_label()
        except MatrixError as e:
            QMessageBox.critical(self, "矩阵错误", str(e))
        except Exception as e:
            logger.error(f"应用选中的矩阵失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"应用选中的矩阵失败: {str(e)}")

    def _on_delete_selected_clicked(self):
        """删除选中的矩阵按钮点击处理"""
        try:
            # 获取选中的文件（通过高亮选择，不是复选框）
            selected_items = self.file_list.selectedItems()

            if not selected_items:
                QMessageBox.warning(self, "警告", "请选择要删除的矩阵！")
                return

            selected_files = [item.text() for item in selected_items]

            # 确认删除
            reply = QMessageBox.question(
                self, "确认", f"确定要删除选中的{len(selected_files)}个矩阵吗？\n"
                f"这将永久删除这些文件，且无法恢复！",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

            if reply == QMessageBox.Yes:
                # 删除文件
                success_count = 0
                error_files = []

                for file in selected_files:
                    try:
                        # 使用matrix_manager提供的删除方法，会自动处理缓存清理
                        if self.matrix_manager.delete_matrix(file):
                            success_count += 1
                            logger.info(f"成功删除矩阵文件: {file}")
                    except Exception as e:
                        error_files.append(f"{file}: {str(e)}")
                        logger.error(f"删除文件失败: {file}, 错误: {str(e)}")

                # 重新加载文件列表
                self._load_excel_files()

                # 更新当前应用文件显示
                self._update_current_files_label()

                # 显示结果消息
                if success_count > 0:
                    if error_files:
                        error_msg = "\n".join(error_files)
                        QMessageBox.warning(self, "部分文件删除失败",
                                            f"成功删除 {success_count} 个文件\n\n"
                                            f"失败文件：\n{error_msg}")
                    else:
                        QMessageBox.information(
                            self, "提示", f"已删除{success_count}个文件！")
                else:
                    error_msg = "\n".join(error_files)
                    QMessageBox.critical(
                        self, "删除失败", f"所有文件删除失败：\n{error_msg}")

        except Exception as e:
            logger.error(f"删除选中的矩阵失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"删除选中的矩阵失败: {str(e)}")

    def _on_import_clicked(self):
        """导入按钮点击处理"""
        try:
            # 打开文件对话框，支持选择多个文件
            file_paths, _ = QFileDialog.getOpenFileNames(
                self, "选择矩阵", "", "矩阵 (*.xlsx)")

            if not file_paths:
                return

            # 记录成功导入的文件数量
            success_count = 0
            error_files = []

            # 导入每个矩阵文件
            for file_path in file_paths:
                try:
                    if self.matrix_manager.import_matrix(file_path):
                        success_count += 1
                except Exception as e:
                    error_files.append(
                        f"{os.path.basename(file_path)}: {str(e)}")

            # 重新加载文件列表
            self._load_excel_files()

            # 显示导入结果
            if success_count > 0:
                if len(error_files) > 0:
                    # 部分成功，部分失败
                    error_msg = "\n".join(error_files)
                    QMessageBox.warning(self, "部分文件导入失败",
                                        f"成功导入 {success_count} 个文件\n\n"
                                        f"失败文件：\n{error_msg}")
                else:
                    # 全部成功
                    QMessageBox.information(self, "提示",
                                            f"成功导入 {success_count} 个矩阵！")
            else:
                # 全部失败
                error_msg = "\n".join(error_files)
                QMessageBox.critical(self, "导入失败",
                                     f"所有文件导入失败：\n{error_msg}")

        except MatrixSecurityError as e:
            QMessageBox.critical(self, "安全错误", str(e))
        except MatrixFormatError as e:
            QMessageBox.critical(self, "格式错误", str(e))
        except MatrixError as e:
            QMessageBox.critical(self, "矩阵错误", str(e))
        except Exception as e:
            logger.error(f"导入矩阵文件失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导入矩阵文件失败: {str(e)}")

    def _on_clear_all_clicked(self):
        """清空所有矩阵按钮点击处理"""
        try:
            # 确认对话框
            reply = QMessageBox.question(
                self, "确认", "确定要清空所有矩阵吗？\n这将永久删除所有文件，且无法恢复！",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

            if reply == QMessageBox.Yes:
                # 清空所有矩阵文件
                if self.matrix_manager.clear_matrix():
                    # 重新加载文件列表
                    self._load_excel_files()
                    # 更新当前应用文件显示
                    self._update_current_files_label()
                    QMessageBox.information(self, "提示", "所有矩阵已清空！")
                else:
                    QMessageBox.warning(self, "警告", "清空矩阵操作未完全成功！")

        except Exception as e:
            logger.error(f"清空所有矩阵失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"清空所有矩阵失败: {str(e)}")

    def _on_cancel_apply_clicked(self):
        """取消应用按钮点击处理"""
        try:
            # 清除当前应用的矩阵文件（会自动清理缓存）
            self.matrix_manager.clear_current_matrix()
            # 更新界面显示
            self._update_current_files_label()
            # 取消所有复选框选中状态
            for i in range(self.file_list.count()):
                item = self.file_list.item(i)
                item.setCheckState(Qt.Unchecked)
            QMessageBox.information(self, "提示", "已取消所有当前应用的矩阵！")
        except Exception as e:
            logger.error(f"取消应用失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"取消应用失败: {str(e)}")
