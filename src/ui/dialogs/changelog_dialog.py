from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt
from src.utils.paths import CHANGELOG_FILE

class ChangelogDialog(QDialog):
    """更新日志弹窗"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("更新日志（Changelog）")
        self.setMinimumSize(700, 500)
        self._init_ui()
        self._load_changelog()

    def _init_ui(self):
        layout = QVBoxLayout()
        self.text_edit = QTextEdit()
        self.text_edit.setReadOnly(True)
        self.text_edit.setLineWrapMode(QTextEdit.NoWrap)
        layout.addWidget(self.text_edit)

        btn_layout = QHBoxLayout()
        btn_layout.addStretch(1)
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        btn_layout.addWidget(close_btn)
        layout.addLayout(btn_layout)
        self.setLayout(layout)

    def _load_changelog(self):
        try:
            with open(CHANGELOG_FILE, 'r', encoding='utf-8') as f:
                content = f.read()
            self.text_edit.setPlainText(content)
        except Exception as e:
            self.text_edit.setPlainText(f"无法加载更新日志: {e}") 