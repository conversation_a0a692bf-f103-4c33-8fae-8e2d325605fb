"""
标签页管理器
"""
from PyQt5.QtWidgets import QTabWidget, QWidget, QMenu, QInputDialog, QMessageBox
from PyQt5.QtCore import Qt
from typing import List

from src.core.config_manager import ConfigManager, ConfigType
from src.utils.paths import CONFIG_DIR

class TabManager:
    """标签页管理器类"""
    
    def __init__(self, tab_widget: QTabWidget):
        """初始化标签页管理器
        
        Args:
            tab_widget: 标签页控件
        """
        self._tab_widget = tab_widget
        self._config = ConfigManager(CONFIG_DIR)
        
        # 设置标签页属性
        self._tab_widget.setTabsClosable(True)  # 启用标签页关闭按钮
        self._tab_widget.tabCloseRequested.connect(self._close_tab)  # 关闭标签的信号连接
        self._tab_widget.setMovable(False)  # 禁止拖动标签
        self._tab_widget.setContextMenuPolicy(Qt.CustomContextMenu)  # 设置上下文菜单策略
        self._tab_widget.customContextMenuRequested.connect(self._show_context_menu)  # 右键菜单连接
        
        # 创建首页
        self._create_home_tab()
        
        # 恢复其他标签页状态
        self._restore_tabs_state()
        
        # 隐藏首页的关闭按钮
        self._tab_widget.tabBar().setTabButton(0, self._tab_widget.tabBar().RightSide, None)
        
    def _create_home_tab(self):
        """创建首页标签页"""
        home_tab = QWidget()
        self._tab_widget.addTab(home_tab, '首页')
        self._tab_widget.setCurrentIndex(0)  # 设置默认显示首页标签页
        
    def _show_context_menu(self, pos):
        """显示右键菜单
        
        Args:
            pos: 鼠标位置
        """
        context_menu = QMenu(self._tab_widget)
        new_tab_action = context_menu.addAction('新建标签页')
        rename_tab_action = context_menu.addAction('重命名标签页')
        delete_tab_action = context_menu.addAction('删除标签页')

        action = context_menu.exec_(self._tab_widget.mapToGlobal(pos))
        if action == new_tab_action:
            self.add_tab('新标签页')
        elif action == rename_tab_action:
            self.rename_tab(self._tab_widget.currentIndex())
        elif action == delete_tab_action:
            self._close_tab(self._tab_widget.currentIndex())
            
    def add_tab(self, title: str) -> bool:
        """添加标签页
        
        Args:
            title: 标签页标题
            
        Returns:
            bool: 是否添加成功
        """
        # 如果是首页，直接添加
        if title == '首页':
            new_tab = QWidget()
            self._tab_widget.addTab(new_tab, title)
            return True

        # 对于新建标签页，弹出输入对话框
        if title == '新标签页':
            while True:
                new_name, ok = QInputDialog.getText(self._tab_widget, '新建标签页', '请输入标签页名称:')
                if not ok:  # 用户点击取消
                    return False
                    
                if not new_name:  # 输入为空
                    QMessageBox.warning(self._tab_widget, "警告", "标签页名称不能为空！")
                    continue
                    
                # 检查是否已存在同名标签页
                if self._tab_exists(new_name):
                    QMessageBox.warning(self._tab_widget, "警告", f"标签页 '{new_name}' 已存在！")
                    continue
                    
                # 输入有效，创建新标签页
                new_tab = QWidget()
                self._tab_widget.addTab(new_tab, new_name)
                self._tab_widget.setCurrentWidget(new_tab)  # 切换到新标签页
                return True
        else:
            # 其他情况（如加载已保存的标签页）
            new_tab = QWidget()
            self._tab_widget.addTab(new_tab, title)
            return True
            
        return False
        
    def rename_tab(self, index: int) -> bool:
        """重命名标签页
        
        Args:
            index: 标签页索引
            
        Returns:
            bool: 是否重命名成功
        """
        if index >= 0:
            current_name = self._tab_widget.tabText(index)
            if current_name == '首页':  # 不允许重命名首页
                QMessageBox.warning(self._tab_widget, "警告", "不能重命名首页！")
                return False
                
            while True:
                new_name, ok = QInputDialog.getText(
                    self._tab_widget, 
                    '重命名标签页', 
                    '输入新名称:', 
                    text=current_name
                )
                if not ok:  # 用户点击取消
                    return False
                    
                if not new_name:  # 输入为空
                    QMessageBox.warning(self._tab_widget, "警告", "标签页名称不能为空！")
                    continue
                    
                # 检查是否已存在同名标签页（排除自己）
                exists = False
                for i in range(self._tab_widget.count()):
                    if i != index and self._tab_widget.tabText(i) == new_name:
                        exists = True
                        break
                        
                if exists:
                    QMessageBox.warning(self._tab_widget, "警告", f"标签页 '{new_name}' 已存在！")
                    continue
                    
                self._tab_widget.setTabText(index, new_name)
                return True
                
        return False
        
    def _close_tab(self, index: int):
        """关闭标签页
        
        Args:
            index: 标签页索引
        """
        # 不允许关闭首页
        if index == 0 or self._tab_widget.tabText(index) == '首页':
            QMessageBox.warning(self._tab_widget, "警告", "不能关闭首页！")
            return
            
        # 弹出确认对话框
        tab_name = self._tab_widget.tabText(index)
        reply = QMessageBox.question(
            self._tab_widget, 
            "确认删除", 
            f"确定要删除\"{tab_name}\"标签页吗？", 
            QMessageBox.Yes | QMessageBox.No, 
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 删除标签页
            self._tab_widget.removeTab(index)
        
    def _tab_exists(self, name: str) -> bool:
        """检查标签页是否存在
        
        Args:
            name: 标签页名称
            
        Returns:
            bool: 是否存在
        """
        for i in range(self._tab_widget.count()):
            if self._tab_widget.tabText(i) == name:
                return True
        return False
        
    def save_tabs_state(self):
        """保存标签页状态"""
        tabs_info = []
        for index in range(self._tab_widget.count()):
            tab_text = self._tab_widget.tabText(index)
            # 确保不会保存重复的标签页名称
            if tab_text not in tabs_info:
                tabs_info.append(tab_text)
        
        # 确保首页在第一位
        if '首页' in tabs_info and tabs_info.index('首页') != 0:
            tabs_info.remove('首页')
            tabs_info.insert(0, '首页')
        elif '首页' not in tabs_info:
            tabs_info.insert(0, '首页')
                
        # 使用ConfigManager保存配置
        self._config.set_value(ConfigType.TABS, "标签列表", tabs_info)
        self._config.set_value(ConfigType.TABS, "默认标签", self._tab_widget.tabText(self._tab_widget.currentIndex()))
        
    def _restore_tabs_state(self):
        """恢复标签页状态"""
        try:
            # 获取保存的标签列表
            tab_list = self._config.get_value(ConfigType.TABS, "标签列表", ["首页"])
            default_tab = self._config.get_value(ConfigType.TABS, "默认标签", "首页")
            
            # 确保首页在第一位
            if '首页' in tab_list and tab_list.index('首页') != 0:
                tab_list.remove('首页')
                tab_list.insert(0, '首页')
            elif '首页' not in tab_list:
                tab_list.insert(0, '首页')
            
            # 创建标签页（跳过首页，因为已经创建）
            for tab_name in tab_list:
                if tab_name != '首页':  # 跳过首页
                    self.add_tab(tab_name)
                
            # 设置默认标签页
            if default_tab in tab_list:
                self._tab_widget.setCurrentIndex(tab_list.index(default_tab))
                
        except Exception as e:
            print(f"恢复标签页状态失败: {e}")
            
    def get_tab_list(self) -> List[str]:
        """获取标签页列表
        
        Returns:
            List[str]: 标签页名称列表
        """
        return [self._tab_widget.tabText(i) for i in range(self._tab_widget.count())] 