"""
常量定义模块

本模块定义了整个应用程序使用的所有常量，包括:
- 设备类型常量
- 通道数量常量
- UI界面相关常量
- 消息处理常量
- CAN通信相关常量
"""

# 设备类型常量：定义支持的USBCAN设备型号
USBCANFD_TYPE = (41, 42, 43)  # USBCANFD系列设备类型
USBCAN_XE_U_TYPE = (20, 21, 31)  # USBCAN-E-U系列设备类型
USBCAN_I_II_TYPE = (3, 4)  # USBCAN I/II代设备类型

# 通道数量常量：定义CAN通道数量的范围
MIN_CHANNEL_NUM = 1  # 最小通道数
MAX_CHANNEL_NUM = 2  # 最大通道数

# UI界面相关常量：定义界面各个组件的尺寸
GRPBOX_WIDTH = 260  # 分组框宽度

# 消息列表各列的宽度
MSGCNT_WIDTH = 50   # 序号列宽度
MSGCHN_WIDTH = 60   # 通道列宽度
MSGID_WIDTH = 80    # 帧ID列宽度
MSGDIR_WIDTH = 60   # 方向列宽度
MSGINFO_WIDTH = 100  # 帧信息列宽度
MSGLEN_WIDTH = 60   # 长度列宽度
MSGDATA_WIDTH = 280  # 数据列宽度

# 消息视图总体尺寸
MSGVIEW_WIDTH = MSGCNT_WIDTH + MSGCHN_WIDTH + MSGID_WIDTH + \
    MSGDIR_WIDTH + MSGINFO_WIDTH + MSGLEN_WIDTH + MSGDATA_WIDTH  # 消息视图总宽度
MSGVIEW_HEIGHT = 300    # 消息视图高度
SENDVIEW_HEIGHT = 150   # 发送视图高度

# 主窗口尺寸
WIDGET_WIDTH = GRPBOX_WIDTH + MSGVIEW_WIDTH + 80   # 主窗口宽度
WIDGET_HEIGHT = MSGVIEW_HEIGHT + SENDVIEW_HEIGHT + 200  # 主窗口高度

# 消息处理常量：定义消息处理相关的限制
MAX_DISPLAY = 1000  # 最大显示消息数量
MAX_RCV_NUM = 10    # 单次最大接收消息数量

# CAN消息类型：定义CAN帧ID类型
CAN_TYPE_STANDARD = 0  # 标准帧（11位ID）
CAN_TYPE_EXTENDED = 1  # 扩展帧（29位ID）

# CAN帧类型：定义CAN帧的数据类型
FRAME_TYPE_DATA = 0    # 数据帧
FRAME_TYPE_REMOTE = 1  # 远程帧

# CAN工作模式：定义CAN控制器的工作模式
MODE_NORMAL = 0  # 正常模式
MODE_LISTEN = 1  # 只听模式

# 发送类型：定义消息发送的方式
SEND_TYPE_NORMAL = 0     # 正常发送
SEND_TYPE_SINGLE = 1     # 单次发送
SEND_TYPE_SELF_RECV = 2  # 自发自收

# CANFD类型：定义CANFD的传输类型（严格遵循ZLG API定义）
CANFD_TYPE_CAN = 0       # 普通CAN
CANFD_TYPE_CANFD = 1     # CANFD

# CANFD BRS标志位（用于控制CANFD BRS功能）
CANFD_BRS_FLAG = 0x01  # CANFD BRS标志位

# 图标相关常量
ICON_BORDER_RADIUS = 25  # 图标圆角半径
