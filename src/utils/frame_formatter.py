"""
帧信息格式化工具类

用于统一接收和发送消息的帧信息显示格式
"""
from typing import List


class FrameFormatter:
    """帧信息格式化工具类"""

    @staticmethod
    def get_frame_info(eff: bool, rtr: bool, is_fd: bool, brs: bool = False) -> str:
        """统一的帧信息格式化函数

        Args:
            eff: 是否为扩展帧
            rtr: 是否为远程帧
            is_fd: 是否为CANFD帧
            brs: 是否启用位速率切换

        Returns:
            str: 格式化后的帧信息字符串
        """
        info = []

        # 帧类型（标准帧/扩展帧）
        if eff:
            info.append("扩展帧")
        else:
            info.append("标准帧")

        # 帧格式（数据帧/远程帧）
        if rtr:
            info.append("远程帧")
        else:
            info.append("数据帧")

        # CANFD相关属性
        if is_fd:
            info.append("CANFD")
            if brs:
                info.append("BRS")

        return " ".join(info)

    @staticmethod
    def get_frame_info_from_msg(msg, is_fd: bool) -> str:
        """从消息对象中获取帧信息

        Args:
            msg: CAN消息对象
            is_fd: 是否为CANFD帧

        Returns:
            str: 格式化后的帧信息字符串
        """
        # 对于ZLGCAN的消息对象
        if hasattr(msg, 'frame'):
            frame = msg.frame
            return FrameFormatter.get_frame_info(
                frame.eff,
                frame.rtr,
                is_fd,
                frame.brs if is_fd else False
            )
        # 直接传入的消息帧
        elif hasattr(msg, 'eff'):
            return FrameFormatter.get_frame_info(
                msg.eff,
                msg.rtr,
                is_fd,
                msg.brs if hasattr(msg, 'brs') and is_fd else False
            )
        # 兼容字典格式
        elif isinstance(msg, dict):
            return FrameFormatter.get_frame_info(
                msg.get('eff', False),
                msg.get('rtr', False),
                is_fd,
                msg.get('brs', False) if is_fd else False
            )
        # 默认返回标准数据帧
        return "标准帧 数据帧"
