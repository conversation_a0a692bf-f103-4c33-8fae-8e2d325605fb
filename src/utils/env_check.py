import os
import sys
import winreg
import subprocess
import pkg_resources
import re
from PyQt5.QtWidgets import QMessageBox

VC_VERSIONS = [
    ("2005", r"SOFTWARE\\Microsoft\\VisualStudio\\8.0\\VC\\VCRedist"),
    ("2008", r"SOFTWARE\\Microsoft\\VisualStudio\\9.0\\VC\\VCRedist"),
    ("2010", r"SOFTWARE\\Microsoft\\VisualStudio\\10.0\\VC\\VCRedist"),
    ("2012", r"SOFTWARE\\Microsoft\\VisualStudio\\11.0\\VC\\Runtimes"),
    ("2013", r"SOFTWARE\\Microsoft\\VisualStudio\\12.0\\VC\\Runtimes"),
]

REQUIRED_PYTHON_VERSION = (3, 10)
REQUIRED_PYTHON_PACKAGES = {'PyQt5'}

ZLG_DRIVER_REG_PATH = r"SYSTEM\\CurrentControlSet\\Services\\ZLGUSBCANFD"
# 更新应用商店注册表路径，支持多种可能的路径
ZLG_DRIVER_APPSTORE_REG_PATHS = [
    r"Software\\Microsoft\\Windows\\CurrentVersion\\CloudStore\\Store\\DefaultAccount\\Current\\default$windows.data.apps.appmetadata$appmetadatalist\\windows.data.apps.appmetadata$zhiyuan usbcanfd driver",
    r"Software\\Microsoft\\Windows\\CurrentVersion\\CloudStore\\Store\\DefaultAccount\\Cloud\\default$windows.data.apps.appmetadata$appmetadatalist\\windows.data.apps.appmetadata$zhiyuan usbcanfd driver",
    # 添加一个正则表达式模式，用于匹配包含GUID的路径
    r"Software\\Microsoft\\Windows\\CurrentVersion\\CloudStore\\Store\\DefaultAccount\\(Current|Cloud)\\{[0-9a-fA-F-]+\}\\$windows\.data\.apps\.appmetadata\$appmetadatalist\\windows\.data\.apps\.appmetadata\$zhiyuan usbcanfd driver"
]
ZLG_DRIVER_INSTALLER = os.path.abspath(os.path.join(
    "resource", "USBCANFD_AllInOne_x86_x64_1.0.0.3.exe"))
ZLG_DRIVER_FILES = [
    r"C:\\Windows\\System32\\drivers\\ZLGUSBCANFD.sys",
    r"C:\\Windows\\SysWOW64\\drivers\\ZLGUSBCANFD.sys"
]

# 添加更多可能的驱动文件路径
ZLG_DRIVER_ADDITIONAL_FILES = [
    r"C:\\Windows\\System32\\ZLGUSBCANFD.dll",
    r"C:\\Windows\\SysWOW64\\ZLGUSBCANFD.dll",
    r"C:\\Program Files\\ZLG\\USBCANFD\\ZLGUSBCANFD.dll",
    r"C:\\Program Files (x86)\\ZLG\\USBCANFD\\ZLGUSBCANFD.dll",
    r"C:\\Program Files (x86)\\ZHIYUAN USBCANFD Driver"  # 添加新的驱动文件夹路径
]


def check_python_env(parent=None):
    """
    检查Python版本和依赖包。
    """
    if sys.version_info < REQUIRED_PYTHON_VERSION:
        msg = f"检测到当前Python版本为{sys.version_info.major}.{sys.version_info.minor}，低于3.10，部分功能可能无法使用！\n请升级到Python 3.10及以上版本。"
        QMessageBox.critical(parent, "Python环境错误", msg)
        sys.exit(1)
    installed = {pkg.key for pkg in pkg_resources.working_set}
    missing = {pkg.lower() for pkg in REQUIRED_PYTHON_PACKAGES} - installed
    if missing:
        msg = f"缺少必要的Python依赖包：{', '.join(missing)}\n请先通过pip安装依赖。"
        QMessageBox.critical(parent, "Python依赖缺失", msg)
        sys.exit(1)


def check_vc_runtime(parent=None):
    """
    检查VC++运行库（2005/2008/2010/2012/2013）是否已安装。
    只要有一个版本存在即视为通过。
    """
    for version, reg_path in VC_VERSIONS:
        for root in (winreg.HKEY_LOCAL_MACHINE,):
            for view_flag in (0, winreg.KEY_WOW64_32KEY, winreg.KEY_WOW64_64KEY):
                try:
                    key = winreg.OpenKey(
                        root, reg_path, 0, winreg.KEY_READ | view_flag)
                    winreg.CloseKey(key)
                    return True
                except Exception:
                    continue
    exe_path = os.path.abspath(os.path.join(
        "resource", "MSVBCRT.AIO.2019.10.19.X86 X64.exe"))
    msg = (
        "未检测到 Microsoft Visual C++ 运行库（2005/2008/2010/2012/2013）。\n"
        "这将导致部分功能无法使用。\n\n"
        "是否立即安装运行库？"
    )
    ret = QMessageBox.warning(
        parent, "环境缺失", msg, QMessageBox.Yes | QMessageBox.No)
    if ret == QMessageBox.Yes:
        subprocess.Popen([exe_path], shell=True)
    sys.exit(1)


def check_zlg_driver_installed() -> bool:
    """
    检查ZLG USBCANFD驱动是否已安装（兼容传统驱动和应用商店安装）。
    返回True表示已安装，False表示未安装。
    """
    # 1. 检查传统驱动服务节点
    try:
        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, ZLG_DRIVER_REG_PATH)
        winreg.CloseKey(key)
        return True
    except Exception:
        pass
    
    # 2. 检查应用商店注册表节点 - 使用多种可能的路径
    for reg_path in ZLG_DRIVER_APPSTORE_REG_PATHS:
        # 如果是正则表达式模式
        if reg_path.startswith(r"Software\\Microsoft\\Windows\\CurrentVersion\\CloudStore\\Store\\DefaultAccount\\(Current|Cloud)\\{[0-9a-fA-F-]+\\}"):
            # 尝试在注册表中查找匹配的路径
            try:
                # 打开CloudStore根键
                cloud_store_key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                                                r"Software\Microsoft\Windows\CurrentVersion\CloudStore\Store\DefaultAccount")
                
                # 遍历子键
                for i in range(winreg.QueryInfoKey(cloud_store_key)[0]):
                    try:
                        subkey_name = winreg.EnumKey(cloud_store_key, i)
                        # 检查是否是Current或Cloud
                        if subkey_name in ["Current", "Cloud"]:
                            subkey = winreg.OpenKey(cloud_store_key, subkey_name)
                            # 遍历子键
                            for j in range(winreg.QueryInfoKey(subkey)[0]):
                                try:
                                    guid_key_name = winreg.EnumKey(subkey, j)
                                    # 检查是否是GUID格式
                                    if re.match(r"\{[0-9a-fA-F-]+\}", guid_key_name):
                                        # 尝试打开完整路径
                                        full_path = f"Software\\Microsoft\\Windows\\CurrentVersion\\CloudStore\\Store\\DefaultAccount\\{subkey_name}\\{guid_key_name}\\$windows.data.apps.appmetadata$appmetadatalist\\windows.data.apps.appmetadata$zhiyuan usbcanfd driver"
                                        try:
                                            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, full_path)
                                            winreg.CloseKey(key)
                                            return True
                                        except Exception:
                                            pass
                                except Exception:
                                    continue
                            winreg.CloseKey(subkey)
                    except Exception:
                        continue
                winreg.CloseKey(cloud_store_key)
            except Exception:
                pass
        else:
            # 直接尝试打开固定路径
            try:
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path)
                winreg.CloseKey(key)
                return True
            except Exception:
                pass
    
    # 3. 检查驱动文件 - 包括基本路径和额外路径
    for path in ZLG_DRIVER_FILES + ZLG_DRIVER_ADDITIONAL_FILES:
        if os.path.exists(path):
            # 如果是文件夹路径，检查文件夹是否存在且不为空
            if os.path.isdir(path):
                if os.listdir(path):  # 检查文件夹是否为空
                    return True
            else:
                return True
    
    # 4. 检查设备管理器中是否有ZLG设备
    try:
        # 使用PowerShell命令检查设备管理器中是否有ZLG设备
        cmd = "Get-PnpDevice | Where-Object { $_.FriendlyName -like '*ZLG*' -or $_.FriendlyName -like '*USBCAN*' } | Select-Object -ExpandProperty Status"
        result = subprocess.run(["powershell", "-Command", cmd], capture_output=True, text=True)
        if result.stdout.strip() and "OK" in result.stdout:
            return True
    except Exception:
        pass
    
    return False


def check_zlg_driver(parent=None):
    """
    检查ZLG USBCANFD驱动是否已安装（兼容传统驱动和应用商店安装）。
    """
    if check_zlg_driver_installed():
        return True
    msg = (
        "未检测到中控USBCANFD驱动，部分功能将无法使用。\n\n"
        "是否立即安装驱动？"
    )
    ret = QMessageBox.warning(
        parent, "驱动缺失", msg, QMessageBox.Yes | QMessageBox.No)
    if ret == QMessageBox.Yes:
        subprocess.Popen([ZLG_DRIVER_INSTALLER], shell=True)
    sys.exit(1)


def check_all_env(parent=None):
    """
    统一环境检查入口：Python、VC++、ZLG驱动。
    """
    check_python_env(parent)
    check_vc_runtime(parent)
    check_zlg_driver(parent)
