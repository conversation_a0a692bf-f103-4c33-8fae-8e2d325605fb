"""
应用程序信息常量
"""
import datetime
import os

# 版本号管理
# 主版本.次版本.修订版本
VERSION_MAJOR = 1    # 主版本：有重大更新时更新
VERSION_MINOR = 1    # 次版本：有功能更新时更新
VERSION_PATCH = 1    # 修订版本：有小改动时更新

# 应用程序信息
APP_NAME = "ZLGCAN_Simulator"  # 应用程序名称
APP_VERSION = f"{VERSION_MAJOR}.{VERSION_MINOR}.{VERSION_PATCH}"
APP_DESCRIPTION = """
这是一个基于Python和PyQt5的ZLGCAN 总线仿真测试工具。
- 支持周立功USBCANFD系列设备；
- 支持自定义发送消息;
- 内置CAN报文计算器；
- 内置进制转换工具。
"""

# 开发者信息
DEVELOPER_NAME = "常孝强"
DEVELOPER_EMAIL = "xia<PERSON><PERSON><PERSON><PERSON>@qq.com"

# 版权信息
current_time = datetime.datetime.now()
CURRENT_YEAR = current_time.year
APP_COPYRIGHT = f"Copyright © {CURRENT_YEAR} {DEVELOPER_NAME}。保留所有权利。"

# 设置默认环境变量（如未外部指定）
os.environ.setdefault("APP_ENV", "dev")


def get_app_env() -> str:
    """获取当前应用环境(dev/prod)"""
    return os.environ.get("APP_ENV", "prod").lower()
