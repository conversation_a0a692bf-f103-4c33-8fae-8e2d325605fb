"""
图标资源管理模块
"""
import os
import base64
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QColor, QPainterPath
from PyQt5.QtCore import QByteArray, QSize, Qt, QRect, QRectF

from src.utils.paths import RESOURCE_DIR
from src.utils.constants import ICON_BORDER_RADIUS

class IconManager:
    _instance = None
    _icon = None
    _base64_data = None
    
    def __init__(self):
        self._read_icon_file()
        
    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = IconManager()
        return cls._instance
        
    def _read_icon_file(self):
        """读取图标文件"""
        try:
            # 读取base64编码的图标文件
            icon_path = os.path.join(RESOURCE_DIR, "icon.txt")
            with open(icon_path, "r") as f:
                self._base64_data = f.read().strip()
        except Exception as e:
            print(f"读取图标文件失败: {e}")
            self._base64_data = None
            
    def _create_rounded_icon(self, pixmap, border_radius=None):
        """创建圆角图标
        
        Args:
            pixmap: 原始图像
            border_radius: 圆角半径，默认使用constants中定义的值
            
        Returns:
            QPixmap: 圆角处理后的图像
        """
        if pixmap.isNull():
            return pixmap
            
        # 如果未指定圆角半径，使用常量中定义的值
        if border_radius is None:
            border_radius = ICON_BORDER_RADIUS
            
        # 创建一个空白透明图像
        target = QPixmap(pixmap.size())
        target.fill(Qt.transparent)
        
        # 创建绘图器
        painter = QPainter(target)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setRenderHint(QPainter.SmoothPixmapTransform)
        
        # 创建圆角路径
        path = QPainterPath()
        rect = QRectF(0, 0, pixmap.width(), pixmap.height())
        path.addRoundedRect(rect, border_radius, border_radius)
        
        # 填充路径
        painter.setClipPath(path)
        painter.drawPixmap(0, 0, pixmap)
        painter.end()
        
        return target
            
    def initialize(self):
        """初始化图标(在QApplication创建后调用)"""
        if self._base64_data and not self._icon:
            try:
                # 解码base64数据
                icon_data = base64.b64decode(self._base64_data)
                
                # 创建QIcon
                pixmap = QPixmap()
                pixmap.loadFromData(QByteArray(icon_data))
                
                # 添加圆角
                rounded_pixmap = self._create_rounded_icon(pixmap)
                self._icon = QIcon(rounded_pixmap)
                
            except Exception as e:
                print(f"加载图标失败: {e}")
                self._icon = None
            
    @property
    def icon(self) -> QIcon:
        """获取图标
        
        Returns:
            QIcon: 应用程序图标
        """
        return self._icon
        
# 创建全局图标管理器实例
icon_manager = IconManager.instance() 