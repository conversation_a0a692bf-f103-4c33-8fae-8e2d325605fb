"""
CAN相关通用工具函数
"""
from typing import Any, Dict
import logging

logger = logging.getLogger("can_utils")


def get_readable_baudrate(timing_value: Any, baudrate_map: Dict[int, str], is_data_baudrate: bool = False) -> str:
    """
    获取可读的波特率字符串

    Args:
        timing_value: timing参数值（可能是整数、字符串或ctypes类型）
        baudrate_map: 波特率映射表
        is_data_baudrate: 是否为数据段波特率（用于CANFD）

    Returns:
        str: 可读的波特率字符串（如"500K"、"1M"等）
    """
    try:
        # 提取ctypes类型的值
        if hasattr(timing_value, 'value'):
            timing_value = timing_value.value

        # 处理字符串类型
        if isinstance(timing_value, str):
            try:
                timing_value = int(timing_value)
            except ValueError:
                return timing_value

        # 直接查找整数键
        if timing_value in baudrate_map:
            return baudrate_map[timing_value]

        # 找不到匹配时，返回原始值
        return f"未知{'数据段' if is_data_baudrate else ''}波特率(timing={timing_value})"

    except Exception as e:
        logger.error(f"获取可读波特率异常: {e}", exc_info=True)
        return f"错误波特率值({timing_value})"


def dlc_to_len(dlc: int) -> int:
    """
    DLC转换为实际数据长度

    Args:
        dlc: DLC值
    Returns:
        int: 实际数据长度
    """
    if dlc <= 8:
        return dlc
    elif dlc == 9:
        return 12
    elif dlc == 10:
        return 16
    elif dlc == 11:
        return 20
    elif dlc == 12:
        return 24
    elif dlc == 13:
        return 32
    elif dlc == 14:
        return 48
    else:
        return 64
