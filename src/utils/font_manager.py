"""
字体管理器
"""
from PyQt5.QtGui import QFont, QFontDatabase
from PyQt5.QtWidgets import QApplication, QMenu, QAction, QFontDialog
from typing import Optional

from src.core.config_manager import ConfigManager, ConfigType
from src.utils.paths import CONFIG_DIR

class FontManager:
    """字体管理器类"""
    
    def __init__(self):
        """初始化字体管理器"""
        self._config = ConfigManager(CONFIG_DIR)
        self._current_font_size = self._config.get_value(ConfigType.UI, "字体.字号", 10)
        self._current_font_name = self._config.get_value(ConfigType.UI, "字体.字体名", "Microsoft YaHei")
        self._font_action = None
        self._available_fonts = []
        
    def initialize(self):
        """初始化字体（在QApplication创建后调用）"""
        app = QApplication.instance()
        if app is None:
            raise RuntimeError("QApplication必须在初始化字体管理器之前创建")
        
        # 获取系统所有可用字体
        self._available_fonts = QFontDatabase().families()
        
        # 检查配置的字体是否在可用字体列表中，如果不在则使用默认字体
        if self._current_font_name not in self._available_fonts:
            self._current_font_name = "Microsoft YaHei"
            self._config.set_value(ConfigType.UI, "字体.字体名", self._current_font_name)
            
        # 创建并设置字体
        font = QFont(self._current_font_name, self._current_font_size)
        app.setFont(font)
        
        # 设置全局样式表
        app.setStyleSheet(self.get_global_style_sheet())
        
    def create_font_action(self) -> QAction:
        """创建字体设置动作
        
        Returns:
            QAction: 字体设置动作
        """
        if self._font_action is None:
            self._font_action = QAction("字体设置")
            self._font_action.triggered.connect(self._show_font_dialog)
                
        return self._font_action
    
    def _show_font_dialog(self):
        """显示字体选择对话框"""
        app = QApplication.instance()
        current_font = app.font()
        
        # 创建字体对话框
        dialog = QFontDialog(current_font, None)
        dialog.setWindowTitle("选择字体")
        
        # 恢复上次保存的对话框大小
        dialog_width = self._config.get_value(ConfigType.UI, "字体对话框.宽度", 0)
        dialog_height = self._config.get_value(ConfigType.UI, "字体对话框.高度", 0)
        if dialog_width > 0 and dialog_height > 0:
            dialog.resize(dialog_width, dialog_height)
        
        # 显示对话框
        if dialog.exec_():
            self.set_font_name(dialog.selectedFont().family())
            self.set_font_size(dialog.selectedFont().pointSize())
        
        # 保存对话框大小
        self._config.set_value(ConfigType.UI, "字体对话框.宽度", dialog.width())
        self._config.set_value(ConfigType.UI, "字体对话框.高度", dialog.height())
        
    def get_current_font_size(self) -> int:
        """获取当前字号
        
        Returns:
            int: 当前字号
        """
        return self._current_font_size
        
    def set_font_size(self, size: int):
        """设置字号
        
        Args:
            size: 字号
        """
        if size != self._current_font_size:
            self._current_font_size = size
            self._config.set_value(ConfigType.UI, "字体.字号", size)
            self._update_application_font()
            
    def get_current_font_name(self) -> str:
        """获取当前字体名称
        
        Returns:
            str: 当前字体名称
        """
        return self._current_font_name
        
    def set_font_name(self, name: str):
        """设置字体名称
        
        Args:
            name: 字体名称
        """
        if name != self._current_font_name:
            self._current_font_name = name
            self._config.set_value(ConfigType.UI, "字体.字体名", name)
            self._update_application_font()
            
    def _update_application_font(self):
        """更新应用程序字体"""
        app = QApplication.instance()
        if app:
            font = QFont(self._current_font_name, self._current_font_size)
            app.setFont(font)
            # 更新全局样式表
            app.setStyleSheet(self.get_global_style_sheet())
            
    def reset_to_default(self):
        """重置为默认字体设置"""
        self.set_font_size(10)
        self.set_font_name("Microsoft YaHei")
        
    def get_global_style_sheet(self) -> str:
        """获取全局样式表，确保所有控件使用一致的字体大小
        
        Returns:
            str: 全局样式表
        """
        font_size_pt = self._current_font_size
        return f"""
            * {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
            }}
            
            QLabel, QCheckBox, QRadioButton {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
            }}
            
            QPushButton {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
                padding: 3px;
            }}
            
            QLineEdit {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
                padding: 3px;
            }}
            
            QComboBox {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
                padding: 3px;
            }}
            
            QTreeWidget {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
            }}
            
            QTreeWidget::item {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
            }}
            
            QHeaderView::section {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
            }}
            
            QMenu {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
            }}
            
            QMenuBar {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
            }}
            
            QTabBar::tab {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
            }}
            
            QStatusBar {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
            }}
            
            QGroupBox {{
                font-family: "{self._current_font_name}";
                font-size: {font_size_pt}pt;
            }}
        """
        
# 创建全局字体管理器实例
font_manager = FontManager() 