#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志模块，提供统一的日志记录功能
所有模块的日志统一保存到一个文件中
"""

import os
import logging
import colorama
import traceback
from logging.handlers import RotatingFileHandler
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
import platform
import sys
from PyQt5.QtCore import QT_VERSION_STR
from PyQt5.QtWidgets import QApplication
from src.utils.app_info import APP_NAME, APP_VERSION
import psutil

from .paths import LOGS_DIR

# 初始化colorama，支持Windows下的颜色显示
colorama.init()


class ColoredFormatter(logging.Formatter):
    """自定义的彩色日志格式化器"""

    # 定义不同日志级别对应的颜色
    COLORS = {
        'DEBUG': colorama.Fore.CYAN,
        'INFO': colorama.Fore.GREEN,
        'WARNING': colorama.Fore.YELLOW,
        'ERROR': colorama.Fore.RED + colorama.Style.BRIGHT,  # 使错误更醒目
        'CRITICAL': colorama.Fore.RED + colorama.Style.BRIGHT + colorama.Back.WHITE  # 使严重错误非常醒目
    }

    def formatException(self, ei) -> str:
        """格式化异常信息

        Args:
            ei: 异常信息元组 (type, value, traceback)

        Returns:
            str: 格式化后的异常信息
        """
        # 获取异常跟踪信息
        exception_lines = traceback.format_exception(*ei)

        # 构建分隔线
        separator = '=' * 120 + '\n'  # 使用更醒目的分隔线

        # 格式化异常信息
        formatted_lines = []
        error_type = None
        error_msg = None

        for line in exception_lines:
            if line.startswith('Traceback'):
                continue
            if line.strip():
                # 提取错误类型和消息
                if ': ' in line and error_type is None:
                    parts = line.strip().split(': ', 1)
                    if len(parts) == 2:
                        error_type = parts[0]
                        error_msg = parts[1]
                        continue
                formatted_lines.append(line.rstrip())

        # 构建错误摘要
        summary = []
        if error_type and error_msg:
            summary.append(f"错误类型: {error_type}")
            summary.append(f"错误信息: {error_msg}")
            summary.append("详细信息:")

        # 组合最终的异常信息
        exception_text = '\n'.join(summary + formatted_lines)
        return f"\n{separator}{exception_text}\n{separator}"

    def format(self, record):
        # 获取对应的颜色代码
        color = self.COLORS.get(record.levelname, '')

        # 如果有异常信息，格式化异常信息
        if record.exc_info:
            record.exc_text = self.formatException(record.exc_info)

        # 获取格式化后的日志消息
        formatted_msg = super().format(record)

        # 如果有对应的颜色，则为整行添加颜色
        if color:
            # 分行处理，保证每一行都有颜色
            lines = formatted_msg.splitlines()
            formatted_lines = [
                f"{color}{line}{colorama.Style.RESET_ALL}" for line in lines]
            formatted_msg = '\n'.join(formatted_lines)

        return formatted_msg


class TimeRangeFileHandler(logging.Handler):
    """自定义日志处理器，按30分钟分割日志文件，文件名包含起止时间，自动写入头部信息。"""

    def __init__(self, logs_dir, max_files=100, buffer_size=1000):
        super().__init__()
        self.logs_dir = logs_dir
        self.current_file = None
        self.current_start = None
        self.current_end = None
        self.stream = None
        self.header_written = False
        self.max_files = max_files
        self.buffer_size = buffer_size
        self.buffer = []
        self._update_file()
        self._cleanup_old_files()

    def _cleanup_old_files(self):
        """清理旧的日志文件，保留最新的max_files个文件"""
        try:
            # 获取所有日志文件
            log_files = [f for f in os.listdir(self.logs_dir)
                         if f.startswith('Log_') and f.endswith('.log')]

            if len(log_files) > self.max_files:
                # 按修改时间排序
                log_files.sort(key=lambda x: os.path.getmtime(
                    os.path.join(self.logs_dir, x)))
                # 删除最旧的文件
                for old_file in log_files[:-self.max_files]:
                    try:
                        os.remove(os.path.join(self.logs_dir, old_file))
                    except Exception as e:
                        print(f"删除旧日志文件失败: {e}")
        except Exception as e:
            print(f"清理旧日志文件失败: {e}")

    def _get_time_range(self, dt):
        # 取整到最近的半点
        minute = (dt.minute // 30) * 30
        start = dt.replace(minute=minute, second=0, microsecond=0)
        end = start + timedelta(minutes=30)
        return start, end

    def _get_log_file(self, start, end):
        return os.path.join(
            self.logs_dir,
            f"Log_{start.strftime('%Y%m%d')}_{start.strftime('%H%M')}-{end.strftime('%H%M')}.log"
        )

    def _write_header(self):
        if self.stream and not self.header_written:
            header = self._generate_header()
            self.stream.write(header)
            self.stream.flush()
            self.header_written = True

    def _generate_header(self):
        """生成日志文件头部信息"""
        try:
            # 获取系统信息
            qt_version = QT_VERSION_STR
            python_version = sys.version.replace('\n', ' ')
            win_ver = platform.platform()
            arch = platform.architecture()[0]
            cpu_count = os.cpu_count()
            memory = psutil.virtual_memory()

            # 头部内容
            header = (
                f"{'='*80}\n"
                f"日志开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"应用程序: {APP_NAME} v{APP_VERSION}\n"
                f"系统信息:\n"
                f"  - 操作系统: {win_ver} {arch}\n"
                f"  - Python版本: {python_version}\n"
                f"{'='*80}\n"
            )
            return header
        except Exception as e:
            print(f"生成日志头部信息失败: {e}")
            return f"{'='*80}\n日志开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n{'='*80}\n"

    def _update_file(self):
        try:
            now = datetime.now()
            start, end = self._get_time_range(now)
            if (self.current_file is None or
                self.current_start != start or
                    self.current_end != end):
                if self.stream:
                    try:
                        self.stream.close()
                    except Exception as e:
                        print(f"关闭日志文件失败: {e}")

                self.current_start = start
                self.current_end = end
                self.current_file = self._get_log_file(start, end)
                os.makedirs(self.logs_dir, exist_ok=True)
                try:
                    self.stream = open(self.current_file,
                                       'a', encoding='utf-8')
                    self.header_written = False
                    self._write_header()
                except Exception as e:
                    print(f"打开日志文件失败: {e}")
                    self.stream = None
        except Exception as e:
            print(f"更新日志文件失败: {e}")

    def emit(self, record):
        """处理日志记录"""
        try:
            self._update_file()
            msg = self.format(record)
            self._write_header()

            # 添加到缓冲区
            self.buffer.append(msg)

            # 当缓冲区达到指定大小时，写入文件
            if len(self.buffer) >= self.buffer_size:
                self._flush_buffer()
        except Exception as e:
            print(f"处理日志记录失败: {e}")

    def _flush_buffer(self):
        """将缓冲区内容写入文件"""
        try:
            if self.stream and self.buffer:
                self.stream.write('\n'.join(self.buffer) + '\n')
                self.stream.flush()
                self.buffer.clear()
        except Exception as e:
            print(f"写入日志缓冲区失败: {e}")

    def close(self):
        """关闭处理器前刷新缓冲区"""
        try:
            self._flush_buffer()
            if self.stream:
                self.stream.close()
        except Exception as e:
            print(f"关闭日志处理器失败: {e}")
        super().close()


class LoggerManager:
    """日志管理器，实现单例模式"""
    _instance = None
    _initialized = False

    # 共享的文件处理器
    _shared_file_handler = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LoggerManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self._initialized = True
        self._loggers = {}

        # 设置默认日志格式，添加文件名和行号
        self.log_format = '%(asctime)s [%(levelname)s] [%(filename)s:%(lineno)d]: %(message)s'
        self.date_format = '%Y-%m-%d %H:%M:%S'

        # 初始化共享的文件处理器
        self._init_shared_file_handler()

    def _init_shared_file_handler(self):
        """初始化共享的文件处理器"""
        if self._shared_file_handler is not None:
            return

        self._shared_file_handler = TimeRangeFileHandler(LOGS_DIR)
        self._shared_file_handler.setFormatter(
            logging.Formatter(self.log_format, self.date_format)
        )

    def get_logger(
        self,
        name: str,
        level: int = None
    ) -> logging.Logger:
        """
        获取或创建一个命名的日志记录器

        Args:
            name: 日志记录器名称
            level: 日志级别，如果为None则根据模块名称设置默认级别

        Returns:
            logging.Logger: 配置好的日志记录器
        """
        if name in self._loggers:
            return self._loggers[name]

        # 根据模块名称设置默认日志级别
        if level is None:
            # 全局统一使用DEBUG级别，方便开发调试
            level = logging.DEBUG

        logger = logging.getLogger(name)
        logger.setLevel(level)

        # 清除已存在的处理器
        logger.handlers.clear()

        # 创建控制台处理器（使用彩色格式化器）
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(
            ColoredFormatter(self.log_format, self.date_format)
        )
        logger.addHandler(console_handler)

        # 添加共享的文件处理器
        logger.addHandler(self._shared_file_handler)

        self._loggers[name] = logger
        return logger


# 创建默认日志记录器
default_logger = LoggerManager().get_logger('app')


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取日志记录器的便捷函数

    Args:
        name: 日志记录器名称，如果为None则返回默认日志记录器

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    if name is None:
        return default_logger
    return LoggerManager().get_logger(name)
