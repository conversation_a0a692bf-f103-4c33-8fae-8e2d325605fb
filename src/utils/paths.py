"""
路径管理模块
"""
import os
import sys
import platform

# 获取项目根目录的绝对路径
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))

# API目录
API_DIR = os.path.join(ROOT_DIR, "api")

# 配置文件目录
CONFIG_DIR = os.path.join(ROOT_DIR, "config")

# 资源文件目录
RESOURCE_DIR = os.path.join(ROOT_DIR, "resource")

# 日志文件目录
LOGS_DIR = os.path.join(ROOT_DIR, "logs")

# 矩阵文件目录
MATRIX_DIR = os.path.join(ROOT_DIR, "matrix")

# 设备信息文件
DEVICE_INFO_FILE = os.path.join(API_DIR, "dev_info.json")

# 最后使用的车型配置文件
LAST_MODEL_FILE = os.path.join(CONFIG_DIR, "last_model.json")

# ZLGCAN动态库文件 - 根据系统架构自动选择
if platform.architecture()[0] == '64bit':
    ZLGCAN_DLL_FILE = os.path.join(API_DIR, "zlgcan_x64", "zlgcan.dll")
else:
    ZLGCAN_DLL_FILE = os.path.join(API_DIR, "zlgcan_x86", "zlgcan.dll")

# 更新日志文件路径
CHANGELOG_FILE = os.path.join(ROOT_DIR, "CHANGELOG.md")


def ensure_dir_exists(dir_path: str):
    """确保目录存在，如果不存在则创建

    Args:
        dir_path: 目录路径
    """
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)


def get_app_data_dir() -> str:
    """获取应用程序数据目录

    Returns:
        str: 应用程序数据目录的路径
    """
    # 在Windows系统下，使用用户的AppData/Local目录
    if sys.platform == 'win32':
        app_data = os.path.join(os.environ['LOCALAPPDATA'], 'ZLGCANTest')
    # 在Linux/Unix系统下，使用用户主目录下的.config目录
    else:
        app_data = os.path.join(os.path.expanduser('~'),
                                '.config', 'ZLGCANTest')

    # 确保目录存在
    ensure_dir_exists(app_data)
    return app_data


# 确保配置目录存在
ensure_dir_exists(CONFIG_DIR)
# 确保资源目录存在
ensure_dir_exists(RESOURCE_DIR)
# 确保日志目录存在
ensure_dir_exists(LOGS_DIR)
# 确保矩阵目录存在
ensure_dir_exists(MATRIX_DIR)
