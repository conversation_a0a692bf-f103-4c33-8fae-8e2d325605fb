"""
消息发送管理器
"""
from typing import Dict, Any, Optional, Callable
from PyQt5.QtCore import QObject, QThread, pyqtSignal, QTimer, QEventLoop
import time

from src.utils.logger import get_logger

# 创建日志记录器
logger = get_logger("message_sender")


class MessageSendThread(QThread):
    """消息发送线程"""
    send_error = pyqtSignal(str)  # 发送错误信号
    send_complete = pyqtSignal()  # 发送完成信号

    def __init__(self, msg_info: Dict[str, Any], send_count: int, send_num: int,
                 send_period: int, send_callback: Callable[[Dict[str, Any]], bool]):
        super().__init__()
        self._msg_info = msg_info.copy()  # 复制消息信息，避免修改原始数据
        self._send_count = send_count
        self._send_num = send_num
        self._send_period = send_period
        self._send_callback = send_callback
        self._stop_flag = False
        self._current_count = 0
        self._base_id = msg_info["id"]
        self._last_send_time = 0  # 上次发送时间
        self._min_interval = 0.001  # 最小发送间隔(ms)

        # 记录发送任务初始化日志
        # 格式化数据内容
        data_str = ' '.join([f"{b:02X}" for b in msg_info.get("data", [])])
        
        # 记录详细帧类型
        frame_info = []
        if msg_info.get("flags", 0) & 0x1:
            frame_info.append("扩展帧")
        else:
            frame_info.append("标准帧")
            
        if msg_info.get("flags", 0) & 0x2:
            frame_info.append("远程帧")
        else:
            frame_info.append("数据帧")
            
        if msg_info.get("is_canfd", False):
            if msg_info.get("flags", 0) & 0x4:
                frame_info.append("BRS启用")
            else:
                frame_info.append("BRS禁用")
        
        logger.info(
            f"初始化发送任务: ID=0x{self._base_id:X}, "
            f"通道={msg_info.get('channel', 0)}, "
            f"类型={'CANFD' if msg_info.get('is_canfd', False) else 'CAN'}, "
            f"帧属性={'/'.join(frame_info)}, "
            f"数据长度={msg_info.get('len', 0)}, "
            f"数据={data_str}, "
            f"发送次数={send_count}, 每次帧数={send_num}, 发送间隔={send_period}ms")

    def run(self):
        """线程执行函数"""
        try:
            # 设置线程优先级 - 移到线程启动后
            logger.info("设置发送线程优先级...")
            self.setPriority(QThread.HighPriority)
            logger.info(f"发送线程优先级设置为: {self.priority()}")

            logger.info("开始发送任务")
            self._last_send_time = time.time() * 1000  # 记录开始时间

            while not self._stop_flag:
                # 计算距离上次发送的时间
                current_time = time.time() * 1000
                elapsed_time = current_time - self._last_send_time

                # 如果距离上次发送时间小于最小间隔，等待
                if elapsed_time < self._min_interval:
                    self.msleep(1)  # 等待1ms
                    continue

                # 发送消息
                if not self._send_messages():
                    break

                # 更新上次发送时间
                self._last_send_time = time.time() * 1000

                # 检查是否需要继续发送
                if self._send_count > 0:
                    if self._current_count >= self._send_count:
                        logger.info(f"发送任务完成: 已发送 {self._current_count} 次")
                        break

                # 等待指定时间，同时检查停止标志
                if self._send_period > 0:
                    # 使用QThread的msleep方法进行精确延时
                    self.msleep(self._send_period)

            # 恢复初始ID
            self._msg_info["id"] = self._base_id
            if not self._stop_flag:  # 只在正常结束时发送完成信号
                logger.info("发送任务正常完成")
                self.send_complete.emit()
            else:
                logger.info("发送任务被手动停止")

        except Exception as e:
            error_msg = f"发送任务异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            if not self._stop_flag:  # 只在非停止状态下发送错误信号
                self.send_error.emit(error_msg)

    def _send_messages(self) -> bool:
        """发送一组消息

        Returns:
            bool: 是否发送成功
        """
        try:
            for _ in range(self._send_num):
                if self._stop_flag:
                    return False

                # 调用发送回调
                if not self._send_callback(self._msg_info):
                    error_msg = f"ID为0x{self._msg_info['id']:X}的报文发送失败!"
                    logger.error(error_msg)
                    if not self._stop_flag:  # 只在非停止状态下发送错误信号
                        self.send_error.emit(error_msg)
                    return False

            if self._send_count > 0:
                self._current_count += 1
                logger.debug(
                    f"完成第 {self._current_count}/{self._send_count} 次发送")

            return True

        except Exception as e:
            error_msg = f"发送消息异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            if not self._stop_flag:  # 只在非停止状态下发送错误信号
                self.send_error.emit(error_msg)
            return False

    def stop(self):
        """停止发送"""
        logger.info("请求停止发送任务")
        self._stop_flag = True


class MessageSender(QObject):
    """消息发送管理器"""

    # 重新定义信号
    send_error = pyqtSignal(str)  # 发送错误信号
    send_complete = pyqtSignal()  # 发送完成信号

    def __init__(self):
        super().__init__()
        self._send_thread: Optional[MessageSendThread] = None
        self._is_stopping = False  # 添加停止标志
        logger.info("消息发送管理器初始化完成")

    def start_sending(self, msg_info: Dict[str, Any], send_count: int, send_num: int, send_period: int,
                      send_callback: Callable[[Dict[str, Any]], bool]):
        """开始发送消息

        Args:
            msg_info: 消息信息
            send_count: 发送次数(-1表示循环发送)
            send_num: 每次发送的帧数
            send_period: 发送间隔(ms)
            send_callback: 发送回调函数,返回发送是否成功
        """
        if self._is_stopping:  # 如果正在停止，不允许启动新的发送
            logger.warning("正在停止上一个发送任务，拒绝新的发送请求")
            return

        # 记录详细的发送参数
        frame_type = []
        if msg_info.get("flags", 0) & 0x1:
            frame_type.append("扩展帧")
        else:
            frame_type.append("标准帧")
        
        if msg_info.get("flags", 0) & 0x2:
            frame_type.append("远程帧")
        else:
            frame_type.append("数据帧")
            
        if msg_info.get("is_canfd", False):
            can_type = "CANFD"
            if msg_info.get("flags", 0) & 0x4:
                frame_type.append("BRS启用")
            else:
                frame_type.append("BRS禁用")
        else:
            can_type = "CAN"
            
        # 格式化数据内容
        data_str = ' '.join([f"{b:02X}" for b in msg_info.get("data", [])])
        
        # 发送类型
        send_type = msg_info.get("send_type", "正常发送")
        
        # 记录详细日志
        logger.info(
            f"开始新的发送任务: ID=0x{msg_info['id']:X}, "
            f"类型={can_type}, 帧属性={'/'.join(frame_type)}, "
            f"数据长度={msg_info.get('len', 0)}, 数据={data_str}, "
            f"发送方式={send_type}, "
            f"发送次数={send_count}, 每次帧数={send_num}, 发送间隔={send_period}ms")

        # 停止现有的发送线程
        self.stop_sending()

        # 创建并启动发送线程
        self._send_thread = MessageSendThread(
            msg_info, send_count, send_num, send_period, send_callback)
        # 连接信号
        self._send_thread.send_error.connect(self._on_send_error)
        self._send_thread.send_complete.connect(self._on_send_complete)
        # 启动线程
        self._send_thread.start()

    def stop_sending(self):
        """停止发送"""
        if self._send_thread and self._send_thread.isRunning():
            self._is_stopping = True  # 设置停止标志
            logger.info("正在停止发送任务...")
            try:
                # 先断开信号连接
                try:
                    self._send_thread.send_error.disconnect()
                    self._send_thread.send_complete.disconnect()
                except Exception:
                    pass  # 忽略断开信号连接时的异常

                # 停止线程
                self._send_thread.stop()
                # 等待线程结束，但最多等待500ms
                if not self._send_thread.wait(500):
                    logger.warning("等待发送线程结束超时，尝试再次等待...")
                    # 如果等待超时，先尝试再等待500ms
                    if not self._send_thread.wait(500):
                        logger.warning("发送线程无法正常停止，强制终止")
                        self._send_thread.terminate()  # 如果还是无法停止，强制终止
                        self._send_thread.wait()  # 确保线程完全停止

                # 清理线程对象
                self._send_thread = None
                logger.info("发送任务已停止")

            finally:
                self._is_stopping = False  # 清除停止标志

    def disconnect_all_signals(self):
        """断开所有信号连接"""
        try:
            # 断开发送线程的信号连接
            if self._send_thread:
                try:
                    self._send_thread.send_error.disconnect()
                    self._send_thread.send_complete.disconnect()
                except Exception:
                    pass

            # 断开自身的信号连接
            try:
                self.send_error.disconnect()
                self.send_complete.disconnect()
            except Exception:
                pass

            logger.debug("已断开所有信号连接")
        except Exception as e:
            logger.error(f"断开信号连接异常: {e}", exc_info=True)

    def _on_send_error(self, error_msg: str):
        """发送错误处理"""
        if not self._is_stopping:  # 只在非停止状态下转发错误信号
            logger.error(f"发送错误: {error_msg}")
            self.send_error.emit(error_msg)

    def _on_send_complete(self):
        """发送完成处理"""
        if not self._is_stopping:  # 只在非停止状态下转发完成信号
            logger.info("发送任务完成")
            self.send_complete.emit()
