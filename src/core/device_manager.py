"""
设备管理模块
"""
from typing import Optional
from src.utils.logger import get_logger
from api.zlgcan import *
from src.utils.constants import *

logger = get_logger("device_manager")


class DeviceManager:
    def __init__(self, zcan):
        self._zcan = zcan
        self._dev_handle = INVALID_DEVICE_HANDLE
        self._is_open = False
        self._cur_dev_info = None
        self._is_canfd = False
        self._res_support = False
        self._dev_info = None

    def open_device(self, dev_type: int, dev_idx: int, dev_info: dict) -> bool:
        if self._is_open:
            logger.warning("设备已经打开")
            return False
        dev_idx = 0
        self._dev_info = dev_info
        logger.info(f"正在打开设备: 类型={dev_type}, 索引={dev_idx}")
        self._dev_handle = self._zcan.OpenDevice(dev_type, dev_idx, 0)
        if self._dev_handle == INVALID_DEVICE_HANDLE:
            logger.error(f"打开设备失败: 类型={dev_type}, 索引={dev_idx}")
            return False
        dev_name = "未知设备"
        for name, info in self._dev_info.items():
            if info["dev_type"] == dev_type:
                self._cur_dev_info = info.copy()
                self._cur_dev_info["name"] = name
                self._cur_dev_info["index"] = dev_idx
                break
        self._is_canfd = dev_type in USBCANFD_TYPE
        self._res_support = dev_type in USBCANFD_TYPE or dev_type in USBCAN_XE_U_TYPE
        if self._is_canfd:
            try:
                ip = self._zcan.GetIProperty(self._dev_handle)
                if ip:
                    channel_num = self._cur_dev_info.get("chn_num", 1)
                    for chn in range(channel_num):
                        self._zcan.SetValue(ip, f"{chn}/clock", "60000000")
                        self._zcan.SetValue(ip, f"{chn}/canfd_standard", "0")
                        if self._res_support:
                            self._zcan.SetValue(
                                ip, f"{chn}/initenal_resistance", "1")
                    self._zcan.ReleaseIProperty(ip)
            except Exception as e:
                logger.error(f"配置CANFD设备参数异常: {e}", exc_info=True)
        self._is_open = True
        logger.info(f"设备打开成功: {dev_name}")
        return True

    def close_device(self):
        if not self._is_open:
            return
        logger.info("正在关闭设备...")
        self._zcan.CloseDevice(self._dev_handle)
        self._is_open = False
        self._cur_dev_info = None
        self._is_canfd = False
        self._res_support = False
        logger.info("设备关闭完成")

    def get_device_info(self) -> Optional[ZCAN_DEVICE_INFO]:
        if not self._is_open:
            return None
        return self._zcan.GetDeviceInf(self._dev_handle)

    def is_device_opened(self, dev_idx: int) -> bool:
        return self._dev_handle != INVALID_DEVICE_HANDLE and self._is_open

    @property
    def dev_handle(self):
        return self._dev_handle

    @property
    def is_open(self):
        return self._is_open

    @property
    def cur_dev_info(self):
        return self._cur_dev_info

    @property
    def is_canfd(self):
        return self._is_canfd

    @property
    def res_support(self):
        return self._res_support
