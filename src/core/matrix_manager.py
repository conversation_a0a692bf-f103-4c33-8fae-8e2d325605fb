"""
Excel矩阵文件管理模块
只负责Excel（.xlsx）文件的导入、删除、清空、切换、当前文件记录等操作
"""

import os
import shutil
import time
import json
from typing import Dict, Optional, Any
from src.utils.paths import MATRIX_DIR, LAST_MODEL_FILE
from src.utils.logger import get_logger
import gc  # 添加gc模块用于强制垃圾回收

logger = get_logger()


class MatrixError(Exception):
    """矩阵相关异常的基类"""

    def __init__(self, message: str = "矩阵操作发生错误"):
        self.message = message
        super().__init__(self.message)


class MatrixSecurityError(MatrixError):
    """矩阵文件安全性检查异常"""

    def __init__(self, message: str = "矩阵文件安全性检查未通过"):
        super().__init__(message)


class MatrixFormatError(MatrixError):
    """矩阵文件格式错误"""

    def __init__(self, message: str = "矩阵文件格式错误"):
        super().__init__(message)


class MatrixManager:
    """
    Excel矩阵文件管理器（单例模式）
    只负责Excel（.xlsx）文件的导入、删除、清空、切换、当前文件记录等管理操作
    """
    _instance = None

    @classmethod
    def get_instance(cls) -> 'MatrixManager':
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self) -> None:
        if MatrixManager._instance is not None:
            logger.warning("MatrixManager是单例类，请使用get_instance()方法获取实例")
            return
        self._matrix_dir = MATRIX_DIR
        self._last_model_file = LAST_MODEL_FILE
        self._current_matrix: Dict[str, str] = {'ICAN': '', 'ISCAN': ''}
        self._pending_parse = False  # 新增标记，记录是否有待处理的解析任务
        self._load_custom_config()
        logger.info("矩阵管理器初始化完成")

    def import_matrix(self, file_path: str) -> bool:
        """
        导入Excel（.xlsx）文件到矩阵目录
        Args:
            file_path: Excel文件路径
        Returns:
            bool: 是否导入成功
        Raises:
            MatrixSecurityError: 文件安全检查未通过
            MatrixFormatError: 文件格式错误
            MatrixError: 其他矩阵相关错误
        """
        filename = os.path.basename(file_path)
        
        # 检查文件名格式
        is_valid, model_name = self._check_filename(filename)
        if not is_valid:
            raise MatrixFormatError(
                "文件名格式不符合要求\n\n"
                "文件名格式要求：\n"
                "1. 必须以车型名开头\n"
                "2. 必须包含网段标识\"ICAN\"或\"ISCAN\"\n"
                "3. 必须以.xlsx结尾\n\n"
                "合法的文件名示例：\n"
                "- E11G_Matrix_ICAN_CDC_海外_v2.1.xlsx\n"
                "- E12REEV-ICAN-CDC-v1.0.xlsx\n"
                "- E12REEV_ISCAN_v2.0.xlsx"
            )
            
        if not self._check_file_security(file_path):
            logger.error(f"文件安全检查未通过: {filename}")
            raise MatrixSecurityError("文件安全检查未通过")
            
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        target_file_path = os.path.join(self._matrix_dir, filename)
        try:
            shutil.copy2(file_path, target_file_path)
            logger.info(f"已将Excel文件复制到矩阵目录: {target_file_path}")
            return True
        except Exception as e:
            logger.error(f"矩阵文件导入失败: {str(e)}")
            raise MatrixError(f"矩阵文件导入失败: {str(e)}")

    def _check_filename(self, filename: str) -> tuple[bool, str]:
        """检查文件名格式是否正确，并返回车型名

        Args:
            filename: 矩阵文件名

        Returns:
            tuple[bool, str]: (是否合格, 车型名)

        文件名格式要求：
        1. 必须以车型名开头
        2. 必须包含网段标识"ICAN"或"ISCAN"
        3. 必须以.xlsx结尾
        """
        try:
            # 去掉后缀
            if filename.endswith('.xlsx'):
                name = filename[:-5]
            else:
                logger.error(f"文件名格式错误: 必须以.xlsx结尾 ({filename})")
                return False, ""

            # 检查是否包含网段标识
            if not ('ICAN' in filename or 'ISCAN' in filename):
                logger.error(f"文件名格式错误: 必须包含网段标识ICAN或ISCAN ({filename})")
                return False, ""

            # 提取车型名（到第一个"_"或"-"之前的部分）
            model_parts = name.split('_')[0].split('-')[0]
            
            # 检查车型名是否有效（例如：E11G, E12REEV等）
            if not model_parts or not model_parts.startswith('E'):
                logger.error(f"文件名格式错误: 无效的车型名 ({filename})")
                return False, ""

            return True, model_parts

        except Exception as e:
            logger.error(f"检查文件名格式失败: {str(e)}")
            return False, ""

    def delete_matrix(self, filename: str) -> bool:
        """
        删除指定的矩阵文件（仅支持.xlsx）
        Args:
            filename: 要删除的矩阵文件名
        Returns:
            bool: 是否删除成功
        Raises:
            FileNotFoundError: 文件不存在
            MatrixError: 其他矩阵相关错误
        """
        file_path = os.path.join(self._matrix_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"矩阵文件不存在: {file_path}")
            raise FileNotFoundError(f"矩阵文件不存在: {filename}")
        # 检查删除的文件是否为当前使用的文件
        is_current = False
        for net_type, current_file in self._current_matrix.items():
            if current_file == filename:
                self._current_matrix[net_type] = ''
                is_current = True
        if is_current:
            self._save_custom_config()
            # 清除解析缓存
            self._clear_parser_cache(filename)
        try:
            os.remove(file_path)
            logger.info(f"已删除矩阵文件: {filename}")
            return True
        except Exception as e:
            logger.error(f"删除矩阵文件失败: {str(e)}")
            raise MatrixError(f"删除矩阵文件失败: {str(e)}")

    def clear_matrix(self) -> bool:
        """
        清空所有矩阵文件（仅支持.xlsx，慎用！）
        Returns:
            bool: 是否清空成功
        """
        try:
            # 备份当前使用的矩阵，用于后续清除缓存
            had_files = self._current_matrix.copy()

            # 清空当前矩阵设置
            self._current_matrix = {'ICAN': '', 'ISCAN': ''}
            self._save_custom_config()

            # 清空解析缓存（如果存在已勾选的文件）
            if had_files.get('ICAN') or had_files.get('ISCAN'):
                self._clear_all_parser_cache()

            # 强制进行垃圾回收，释放文件句柄
            gc.collect()
            time.sleep(0.5)  # 给系统一些时间来释放资源

            # 获取所有Excel文件
            files = self.list_matrix_files()
            if not files:
                logger.info("没有矩阵文件需要清空")
                return True

            # 删除所有文件，添加重试机制
            max_retries = 3
            failed_files = []
            
            for filename in files:
                file_path = os.path.join(self._matrix_dir, filename)
                success = False
                
                for attempt in range(max_retries):
                    try:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            logger.info(f"已删除矩阵文件: {filename}")
                            success = True
                            break
                    except Exception as e:
                        if attempt < max_retries - 1:
                            time.sleep(1)  # 等待一秒后重试
                            gc.collect()  # 每次重试前强制垃圾回收
                        else:
                            logger.error(f"删除矩阵文件失败: {filename}, 错误: {str(e)}")
                            failed_files.append(filename)
                
                if not success:
                    failed_files.append(filename)

            return len(failed_files) == 0

        except Exception as e:
            logger.error(f"清空所有矩阵文件失败: {str(e)}")
            return False

    def list_matrix_files(self) -> list:
        """
        列出所有Excel（.xlsx）文件
        Returns:
            list: Excel文件名列表
        """
        try:
            if not os.path.exists(self._matrix_dir):
                return []
            return [f for f in os.listdir(self._matrix_dir) if f.lower().endswith('.xlsx')]
        except Exception as e:
            logger.error(f"列出Excel文件失败: {str(e)}")
            return []

    def set_current_matrix(self, ican_file: Optional[str] = None, iscan_file: Optional[str] = None) -> None:
        """
        设置当前使用的ICAN和ISCAN矩阵文件
        Args:
            ican_file: ICAN文件名
            iscan_file: ISCAN文件名
        """
        # 获取当前已设置的文件，用于后续比较是否变化
        old_ican = self._current_matrix.get('ICAN', '')
        old_iscan = self._current_matrix.get('ISCAN', '')

        # 检查文件是否在列表中
        if ican_file and ican_file not in self.list_matrix_files():
            logger.warning(f"ICAN文件不存在: {ican_file}")
            ican_file = ''
        if iscan_file and iscan_file not in self.list_matrix_files():
            logger.warning(f"ISCAN文件不存在: {iscan_file}")
            iscan_file = ''

        # 设置新文件
        self._current_matrix = {
            'ICAN': ican_file or '',
            'ISCAN': iscan_file or ''
        }
        self._save_custom_config()
        logger.info(f"设置当前矩阵文件: ICAN={ican_file}, ISCAN={iscan_file}")

        # 处理缓存清理
        # 如果文件被取消选择，清除对应缓存
        if old_ican and not ican_file:
            self._clear_parser_cache(old_ican)

        if old_iscan and not iscan_file:
            self._clear_parser_cache(old_iscan)

        # 延迟导入，彻底消除循环依赖
        from src.core.matrix_parser import MatrixParser
        parser = MatrixParser.get_instance()

        # 只对新设置的有效文件进行解析
        new_ican = self._current_matrix.get('ICAN', '')
        if new_ican and new_ican != old_ican:
            logger.debug(f"ICAN矩阵文件已变更，开始解析: {new_ican}")
            parser.get_matrix_messages("ICAN")

        new_iscan = self._current_matrix.get('ISCAN', '')
        if new_iscan and new_iscan != old_iscan:
            logger.debug(f"ISCAN矩阵文件已变更，开始解析: {new_iscan}")
            parser.get_matrix_messages("ISCAN")

    def get_current_matrix(self) -> Dict[str, str]:
        """
        获取当前使用的ICAN和ISCAN矩阵文件
        Returns:
            dict: {'ICAN': ..., 'ISCAN': ...}
        """
        return self._current_matrix.copy()

    def _save_custom_config(self):
        try:
            config = {
                "version": "1.0",
                "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
                "matrix_files": self._current_matrix
            }
            with open(self._last_model_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            logger.debug(f"保存矩阵配置: {self._current_matrix}")
        except Exception as e:
            logger.error(f"保存矩阵配置失败: {str(e)}")

    def _load_custom_config(self):
        try:
            if os.path.isfile(self._last_model_file):
                with open(self._last_model_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    matrix_files = data.get('matrix_files', {})

                    if matrix_files and isinstance(matrix_files, dict):
                        ican_file = matrix_files.get('ICAN', '')
                        iscan_file = matrix_files.get('ISCAN', '')
                        if ican_file and ican_file in self.list_matrix_files():
                            self._current_matrix['ICAN'] = ican_file
                        else:
                            self._current_matrix['ICAN'] = ''
                        if iscan_file and iscan_file in self.list_matrix_files():
                            self._current_matrix['ISCAN'] = iscan_file
                        else:
                            self._current_matrix['ISCAN'] = ''
                        logger.debug(
                            f"读取到上次使用的矩阵文件: ICAN={ican_file}, ISCAN={iscan_file}")

                        # 启动时检测到已勾选的Excel文件
                        if self._current_matrix['ICAN'] or self._current_matrix['ISCAN']:
                            logger.debug("启动时检测到已勾选Excel文件，稍后自动解析...")
                            # 移除定时器，只使用主窗口显示后的解析
                            # import threading
                            # timer = threading.Timer(
                            #     1.0, self._trigger_parse_on_startup)
                            # timer.daemon = True
                            # timer.start()

                            # 只设置标记，在应用程序主循环正式启动后再解析
                            self._pending_parse = True
        except Exception as e:
            logger.error(f"读取自定义矩阵配置失败: {str(e)}")

    def _trigger_parse_on_startup(self):
        """程序启动时触发Excel解析的辅助方法"""
        try:
            # 首先检查是否已在执行解析，避免重复执行
            if hasattr(self, '_is_parsing') and self._is_parsing:
                logger.debug("已有解析任务在执行中，跳过本次解析")
                return

            self._is_parsing = True  # 设置解析状态标记

            # 强制输出日志，确保信息可见
            logger.debug("开始执行延迟解析任务...")

            # 延迟导入，避免循环依赖
            from src.core.matrix_parser import MatrixParser
            parser = MatrixParser.get_instance()

            # 解析已勾选的Excel文件
            if self._current_matrix['ICAN']:
                logger.debug(
                    f"启动时自动解析ICAN文件: {self._current_matrix['ICAN']}")
                parser.get_matrix_messages("ICAN")
            if self._current_matrix['ISCAN']:
                logger.debug(
                    f"启动时自动解析ISCAN文件: {self._current_matrix['ISCAN']}")
                parser.get_matrix_messages("ISCAN")

            # 修改标记，避免重复解析
            self._pending_parse = False
            self._is_parsing = False  # 解析完成，重置状态
            logger.debug("延迟解析任务执行完成")
        except Exception as e:
            # 确保发生异常时也重置状态
            self._is_parsing = False
            self._pending_parse = False
            logger.error(f"启动时自动解析Excel文件失败: {str(e)}")

    def parse_if_needed(self):
        """
        提供给外部调用的方法，在应用程序主循环已启动后检查是否需要解析
        在主窗口初始化完成后调用此方法
        """
        if hasattr(self, '_pending_parse') and self._pending_parse:
            logger.debug("主窗口初始化完成后，检测到需要解析Excel文件")
            self._trigger_parse_on_startup()

    def _check_file_security(self, file_path: str) -> bool:
        try:
            if os.path.getsize(file_path) > 10 * 1024 * 1024:
                raise MatrixSecurityError("文件大小超过限制(10MB)")
            if not file_path.lower().endswith('.xlsx'):
                raise MatrixSecurityError("非法的文件扩展名，必须是.xlsx")
            return True
        except MatrixSecurityError:
            raise
        except Exception as e:
            logger.error(f"文件安全性检查失败: {str(e)}")
            return False

    def sync_current_matrix_with_files(self) -> bool:
        """
        检查当前记录的ICAN/ISCAN文件是否真实存在（仅支持.xlsx），不存在则清空并保存配置。
        Returns:
            bool: 是否有变更（即是否有文件被清空）
        """
        changed = False
        for net in ['ICAN', 'ISCAN']:
            filename = self._current_matrix.get(net, '')
            if filename and filename not in self.list_matrix_files():
                self._current_matrix[net] = ''
                changed = True
        if changed:
            self._save_custom_config()
        return changed

    def load_specified_files(self, ican_file: Optional[str] = None, iscan_file: Optional[str] = None) -> dict:
        """
        兼容UI调用：设置ICAN和ISCAN文件，并返回加载结果（仅支持.xlsx）
        """
        result = {
            "ICAN": {"file": ican_file, "success": False, "error": ""},
            "ISCAN": {"file": iscan_file, "success": False, "error": ""}
        }

        # 获取当前已设置的文件，用于后续比较是否变化
        old_ican = self._current_matrix.get('ICAN', '')
        old_iscan = self._current_matrix.get('ISCAN', '')

        files = self.list_matrix_files()
        if ican_file and ican_file in files:
            self._current_matrix['ICAN'] = ican_file
            result["ICAN"]["success"] = True
        else:
            self._current_matrix['ICAN'] = ''
            if ican_file:
                result["ICAN"]["error"] = f"ICAN文件不存在: {ican_file}"
        if iscan_file and iscan_file in files:
            self._current_matrix['ISCAN'] = iscan_file
            result["ISCAN"]["success"] = True
        else:
            self._current_matrix['ISCAN'] = ''
            if iscan_file:
                result["ISCAN"]["error"] = f"ISCAN文件不存在: {iscan_file}"
        self._save_custom_config()

        # 只有当文件发生变化并且文件有效时才调用解析
        # 延迟导入，彻底消除循环依赖
        from src.core.matrix_parser import MatrixParser
        parser = MatrixParser.get_instance()

        new_ican = self._current_matrix.get('ICAN', '')
        if new_ican and new_ican != old_ican:
            logger.debug(f"ICAN矩阵文件已变更，开始解析: {new_ican}")
            parser.get_matrix_messages("ICAN")

        new_iscan = self._current_matrix.get('ISCAN', '')
        if new_iscan and new_iscan != old_iscan:
            logger.debug(f"ISCAN矩阵文件已变更，开始解析: {new_iscan}")
            parser.get_matrix_messages("ISCAN")

        return result

    def clear_current_matrix(self) -> None:
        """
        取消当前已应用的ICAN和ISCAN矩阵文件（即全部置空）
        """
        # 保存当前值，以便确认是否真的发生了变化
        had_ican = bool(self._current_matrix.get('ICAN', ''))
        had_iscan = bool(self._current_matrix.get('ISCAN', ''))

        # 如果需要清除解析缓存，则保存旧文件名
        ican_file = self._current_matrix.get('ICAN', '')
        iscan_file = self._current_matrix.get('ISCAN', '')

        # 清空设置
        self._current_matrix = {'ICAN': '', 'ISCAN': ''}
        self._save_custom_config()

        # 如果有文件被取消，则清除对应的解析缓存
        if had_ican or had_iscan:
            logger.debug("已取消所有当前应用的矩阵文件")
            # 清除解析缓存
            if ican_file:
                self._clear_parser_cache(ican_file)
            if iscan_file:
                self._clear_parser_cache(iscan_file)
        else:
            logger.debug("没有需要取消的矩阵文件，当前已为空")

    def _clear_parser_cache(self, filename: str) -> None:
        """
        清除指定文件的解析缓存
        Args:
            filename: 文件名
        """
        try:
            # 延迟导入，避免循环依赖
            from src.core.matrix_parser import MatrixParser
            parser = MatrixParser.get_instance()

            # 如果文件在缓存中，清除缓存
            if hasattr(parser, '_xlsx_cache') and filename in parser._xlsx_cache:
                del parser._xlsx_cache[filename]
                logger.debug(f"已清除Excel解析缓存: {filename}")

            if hasattr(parser, '_messages_cache') and filename in parser._messages_cache:
                del parser._messages_cache[filename]
                logger.debug(f"已清除消息缓存: {filename}")

            if hasattr(parser, '_messages_by_name_cache') and filename in parser._messages_by_name_cache:
                del parser._messages_by_name_cache[filename]
                logger.debug(f"已清除名称索引缓存: {filename}")
        except Exception as e:
            logger.error(f"清除解析缓存失败: {str(e)}")

    def _clear_all_parser_cache(self) -> None:
        """清除所有解析缓存"""
        try:
            # 延迟导入，避免循环依赖
            from src.core.matrix_parser import MatrixParser
            parser = MatrixParser.get_instance()
            parser.clear_cache()
            logger.debug("已清除所有解析缓存")
        except Exception as e:
            logger.error(f"清除所有解析缓存失败: {str(e)}")
