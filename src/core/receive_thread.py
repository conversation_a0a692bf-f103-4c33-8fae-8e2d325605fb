"""
CAN消息接收线程模块
"""
import time
import threading
from datetime import datetime
from PyQt5.QtCore import QThread, pyqtSignal
from src.utils.logger import get_logger
from src.utils.frame_formatter import FrameFormatter
from api.zlgcan import ZCAN_TYPE_CAN, ZCAN_TYPE_CANFD


class MessageReceiveThread(QThread):
    """CAN消息接收线程"""
    message_received = pyqtSignal(list)

    def __init__(self, can_handle, zcan, dev_info, channel_idx, msg_callback=None):
        super().__init__()
        self._can_handle = can_handle
        self._zcan = zcan
        self._dev_info = dev_info
        self._channel_idx = channel_idx
        self._terminated = False
        self._msg_received_callback = msg_callback
        self._msg_buffer = []
        self._buffer_lock = threading.Lock()
        self._total_received = 0
        self._start_time = 0
        self._last_stat_time = 0
        self._stat_interval = 5
        self._batch_size = 1000
        self._max_buffer_size = 10000
        self.logger = get_logger(f"receive_thread_{channel_idx}")

    def run(self):
        self.logger.info(f"通道{self._channel_idx}接收线程启动")
        self._start_time = time.time()
        self._last_stat_time = self._start_time
        try:
            while not self._terminated:
                can_num = self._zcan.GetReceiveNum(
                    self._can_handle, ZCAN_TYPE_CAN)
                canfd_num = self._zcan.GetReceiveNum(
                    self._can_handle, ZCAN_TYPE_CANFD)
                if not can_num and not canfd_num:
                    self.msleep(1)
                    if time.time() - self._last_stat_time >= self._stat_interval:
                        self._output_stats()
                        self._last_stat_time = time.time()
                    continue
                if can_num:
                    self._receive_messages(can_num, False)
                if canfd_num:
                    self._receive_messages(canfd_num, True)
                self._send_buffered_messages()
                if time.time() - self._last_stat_time >= self._stat_interval:
                    self._output_stats()
                    self._last_stat_time = time.time()
        except Exception as e:
            self.logger.error(
                f"通道{self._channel_idx}接收线程异常: {e}", exc_info=True)

    def _output_stats(self):
        elapsed = time.time() - self._start_time
        if elapsed > 0:
            rate = self._total_received / elapsed
            self.logger.info(
                f"通道{self._channel_idx}接收统计: 已接收{self._total_received}条, 速率: {rate:.2f}条/秒")

    def _receive_messages(self, msg_num: int, is_fd: bool):
        try:
            batch_size = min(msg_num, self._batch_size)
            if is_fd:
                rcv_msgs, act_num = self._zcan.ReceiveFD(
                    self._can_handle, batch_size)
            else:
                rcv_msgs, act_num = self._zcan.Receive(
                    self._can_handle, batch_size)
            if not act_num:
                return
            self._total_received += act_num
            with self._buffer_lock:
                for i in range(act_num):
                    msg = rcv_msgs[i]
                    msg_dict = {
                        "device": self._dev_info["index"],
                        "channel": self._channel_idx,
                        "id": f"{msg.frame.can_id:03X}",
                        "direction": "接收",
                        "info": FrameFormatter.get_frame_info(
                            msg.frame.eff,
                            msg.frame.rtr,
                            is_fd,
                            msg.frame.brs if is_fd else False
                        ),
                        "dlc": msg.frame.len if is_fd else msg.frame.can_dlc,
                        "data": ' '.join([f"{msg.frame.data[j]:02X}" for j in range(
                            msg.frame.len if is_fd else msg.frame.can_dlc
                        )]),
                        "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    }
                    self._msg_buffer.append(msg_dict)
        except Exception as e:
            self.logger.error(f"接收消息异常: {e}", exc_info=True)

    def _send_buffered_messages(self):
        try:
            with self._buffer_lock:
                if not self._msg_buffer:
                    return
                messages = self._msg_buffer
                self._msg_buffer = []
            messages.sort(key=lambda x: x["timestamp"])
            self.message_received.emit(messages)
        except Exception as e:
            self.logger.error(f"发送缓冲消息异常: {e}", exc_info=True)

    def stop(self):
        self.logger.info(f"通道{self._channel_idx}接收线程停止")
        self._terminated = True
        self.wait()
