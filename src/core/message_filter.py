"""
消息过滤器
"""
from typing import Dict, Any

class MessageFilter:
    """消息过滤器类"""
    
    def __init__(self):
        self.is_paused = False
        self.channel_filter = "全部"
        self.id_filter = ""
        self.direction_filter = "全部"
        
    def set_channel_filter(self, channel: str):
        """设置通道过滤器
        
        Args:
            channel: 通道名称
        """
        self.channel_filter = channel
        
    def set_id_filter(self, id_filter: str):
        """设置ID过滤器
        
        Args:
            id_filter: ID过滤字符串
        """
        self.id_filter = id_filter
        
    def set_direction_filter(self, direction: str):
        """设置方向过滤器
        
        Args:
            direction: 方向("全部"/"发送"/"接收")
        """
        self.direction_filter = direction
        
    def reset_filters(self):
        """重置所有过滤器"""
        self.channel_filter = "全部"
        self.id_filter = ""
        self.direction_filter = "全部"
        
    def apply_filter(self, msg_dict: dict) -> bool:
        """应用过滤器
        
        Args:
            msg_dict: 消息字典
            
        Returns:
            bool: 是否通过过滤
        """
        if self.is_paused:
            return False
            
        # 通道过滤
        if self.channel_filter != "全部":
            channel_key = f"设备{msg_dict['device']} 通道{msg_dict['channel']}"
            if channel_key != self.channel_filter:
                return False
                
        # ID过滤
        if self.id_filter and self.id_filter.lower() not in msg_dict["id"].lower():
            return False
            
        # 方向过滤
        if self.direction_filter != "全部" and msg_dict["direction"] != self.direction_filter:
            return False
            
        return True 