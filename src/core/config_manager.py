"""
配置管理器
"""
from enum import Enum
from typing import Any, Dict, Optional, List
import json
import os
from datetime import datetime

class ConfigType(Enum):
    """配置类型枚举"""
    WINDOW_LAYOUT = "window_layout.json"
    MESSAGE_PANEL = "message_panel.json"
    CHANNEL = "channel_config.json"
    TABS = "tabs_config.json"
    DBC = "dbc_config.json"
    UI = "ui_settings.json"

class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_dir: str):
        """初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self._config_dir = config_dir
        self._configs: Dict[ConfigType, Dict] = {}
        self._default_configs = self._init_default_configs()
        
        # 确保配置目录存在
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)
            
    def _init_default_configs(self) -> Dict[ConfigType, Dict]:
        """初始化默认配置"""
        return {
            ConfigType.WINDOW_LAYOUT: {
                "version": "1.0",
                "last_update": "",
                "窗口": {
                    "宽度": 1024,
                    "高度": 768,
                    "X坐标": 100,
                    "Y坐标": 100
                },
                "分隔器": {
                    "左侧宽度": 300,
                    "右侧宽度": 700
                }
            },
            ConfigType.MESSAGE_PANEL: {
                "version": "1.0",
                "last_update": "",
                "列宽": {
                    "序号": 84,
                    "时间": 157,
                    "通道": 123,
                    "帧ID": 80,
                    "方向": 60,
                    "帧信息": 217,
                    "长度": 60,
                    "数据": -1  # -1表示自动调整
                },
                "过滤器": {
                    "默认通道": "全部",
                    "默认方向": "全部"
                },
                "显示设置": {
                    "最大显示条数": 1000,
                    "自动滚动": True
                }
            },
            ConfigType.CHANNEL: {
                "version": "1.0",
                "last_update": "",
                "通道状态": {},
                "默认设置": {
                    "波特率": 500000,
                    "工作模式": "正常",
                    "终端电阻": False
                }
            },
            ConfigType.TABS: {
                "version": "1.0",
                "last_update": "",
                "标签列表": ["首页"],
                "默认标签": "首页"
            },
            ConfigType.DBC: {
                "version": "1.0",
                "last_update": "",
                "当前车型": "",
                "最近使用": [],
                "最大历史记录": 10
            },
            ConfigType.UI: {
                "version": "1.0",
                "last_update": "",
                "字体": {
                    "字号": 10,
                    "字体名": "Microsoft YaHei"
                },
                "字体对话框": {
                    "宽度": 0,
                    "高度": 0
                },
                "主题": "默认"
            }
        }
        
    def load_config(self, config_type: ConfigType) -> Dict:
        """加载指定类型的配置
        
        Args:
            config_type: 配置类型
            
        Returns:
            Dict: 配置数据
        """
        if config_type not in self._configs:
            config_path = os.path.join(self._config_dir, config_type.value)
            try:
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        loaded_config = json.load(f)
                        # 合并默认配置
                        self._configs[config_type] = self._merge_configs(
                            self._default_configs[config_type],
                            loaded_config
                        )
                else:
                    # 使用默认配置
                    self._configs[config_type] = self._default_configs[config_type].copy()
                    # 设置初始更新时间
                    self._configs[config_type]["last_update"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    # 保存默认配置
                    self.save_config(config_type)
            except Exception as e:
                print(f"加载配置失败 {config_type.value}: {e}")
                # 使用默认配置
                self._configs[config_type] = self._default_configs[config_type].copy()
                
        return self._configs[config_type]
        
    def save_config(self, config_type: ConfigType):
        """保存指定类型的配置
        
        Args:
            config_type: 配置类型
        """
        if config_type in self._configs:
            config_path = os.path.join(self._config_dir, config_type.value)
            try:
                # 更新最后修改时间
                self._configs[config_type]["last_update"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(self._configs[config_type], f, ensure_ascii=False, indent=4)
            except Exception as e:
                print(f"保存配置失败 {config_type.value}: {e}")
                
    def get_value(self, config_type: ConfigType, key_path: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            config_type: 配置类型
            key_path: 键路径，使用.分隔，如"窗口.宽度"
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        config = self.load_config(config_type)
        keys = key_path.split('.')
        value = config
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
            
    def set_value(self, config_type: ConfigType, key_path: str, value: Any):
        """设置配置值
        
        Args:
            config_type: 配置类型
            key_path: 键路径，使用.分隔，如"窗口.宽度"
            value: 配置值
        """
        config = self.load_config(config_type)
        keys = key_path.split('.')
        target = config
        
        # 遍历到最后一个键之前
        for key in keys[:-1]:
            if key not in target:
                target[key] = {}
            target = target[key]
            
        # 设置最后一个键的值
        target[keys[-1]] = value
        self.save_config(config_type)
        
    def _merge_configs(self, default_config: Dict, loaded_config: Dict) -> Dict:
        """合并配置，确保所有默认配置项都存在
        
        Args:
            default_config: 默认配置
            loaded_config: 加载的配置
            
        Returns:
            Dict: 合并后的配置
        """
        result = default_config.copy()
        
        def merge_dict(target: Dict, source: Dict):
            for key, value in source.items():
                if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                    merge_dict(target[key], value)
                else:
                    target[key] = value
                    
        merge_dict(result, loaded_config)
        return result
        
    def get_all_configs(self) -> Dict[str, Dict]:
        """获取所有配置
        
        Returns:
            Dict[str, Dict]: 所有配置的字典
        """
        return {config_type.value: self.load_config(config_type) 
                for config_type in ConfigType}
                
    def reset_config(self, config_type: ConfigType):
        """重置指定类型的配置为默认值
        
        Args:
            config_type: 配置类型
        """
        if config_type in self._configs:
            del self._configs[config_type]
        self.load_config(config_type)  # 这将加载默认配置
        self.save_config(config_type)
        
    def add_to_recent_list(self, config_type: ConfigType, key_path: str, value: str, max_items: int = 10):
        """添加项目到最近使用列表
        
        Args:
            config_type: 配置类型
            key_path: 最近使用列表的键路径
            value: 要添加的值
            max_items: 最大项目数
        """
        recent_list = self.get_value(config_type, key_path, [])
        if value in recent_list:
            recent_list.remove(value)
        recent_list.insert(0, value)
        if len(recent_list) > max_items:
            recent_list = recent_list[:max_items]
        self.set_value(config_type, key_path, recent_list) 