"""
Excel矩阵文件解析模块
负责解析Excel（.xlsx）文件的内容，提取报文、信号等信息
"""

import os
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from openpyxl import load_workbook
from src.utils.paths import MATRIX_DIR
from src.utils.logger import get_logger
from src.core.matrix_manager import MatrixManager, MatrixError
import dataclasses
import json

# 若有 get_app_env 可用则导入，否则用环境变量
try:
    from src.utils.app_info import get_app_env
except ImportError:
    def get_app_env():
        return os.environ.get("APP_ENV", "dev")

logger = get_logger()


@dataclass
class Signal:
    """信号定义类，对应Excel H~W列"""
    signal_name: str
    signal_description: str
    byte_order: str
    start_bit: int
    signal_send_type: str
    bit_length: int
    data_type: str
    resolution: float
    offset: float
    min_value: Optional[float]
    max_value: Optional[float]
    initial_value: str
    invalid_value: str
    inactive_value: str
    unit: str
    value_description: str
    # 关联报文属性
    msg_id: str
    msg_name: str
    msg_type: str
    msg_send_type: str
    msg_cycle_time: str
    frame_format: str
    msg_length: int
    msg_cycle_time_fast: str
    msg_nrof_reption: str
    msg_delay_time: str
    node_role: str
    comment: str


@dataclass
class Message:
    """报文定义类，对应Excel A~G、X~AA列"""
    msg_name: str
    msg_type: str
    msg_id: str
    msg_send_type: str
    msg_cycle_time: str
    frame_format: str
    msg_length: int
    msg_cycle_time_fast: str
    msg_nrof_reption: str
    msg_delay_time: str
    node_role: str
    comment: str
    signals: List[Signal] = field(default_factory=list)


class MatrixParseError(MatrixError):
    """矩阵解析错误"""

    def __init__(self, message: str = "矩阵文件解析错误"):
        super().__init__(message)


class MatrixParser:
    """
    Excel矩阵文件解析器（单例模式）
    负责解析Excel文件，提取报文、信号等信息
    """
    _instance = None

    @classmethod
    def get_instance(cls) -> 'MatrixParser':
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self) -> None:
        if MatrixParser._instance is not None:
            logger.warning("MatrixParser是单例类，请使用get_instance()方法获取实例")
            return
        self._matrix_dir = MATRIX_DIR
        self._matrix_manager = MatrixManager.get_instance()
        self._xlsx_cache: Dict[str, Any] = {}
        # {文件名: {msg_id: Message}}
        self._messages_cache: Dict[str, Dict[str, Message]] = {}
        # {文件名: {msg_name: Message}}
        self._messages_by_name_cache: Dict[str, Dict[str, Message]] = {}
        logger.info("Excel矩阵解析器初始化完成")

    def _save_to_json(self, file_name: str, messages_by_id: Dict[str, Message]):
        """开发环境下将解析结果保存为matrix/*.json"""
        json_path = os.path.join(
            self._matrix_dir, os.path.splitext(file_name)[0] + '.json')
        try:
            serializable = {}
            for k, v in messages_by_id.items():
                msg_dict = dataclasses.asdict(v)
                msg_dict['signals'] = [
                    dataclasses.asdict(sig) for sig in v.signals]
                serializable[k] = msg_dict
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(serializable, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存解析结果到: {json_path}")
        except Exception as e:
            logger.error(f"保存解析结果到json失败: {str(e)}")

    def _parse_xlsx_file(self, file_name: str) -> Tuple[Dict[str, Message], Dict[str, Message]]:
        """
        解析Excel文件，返回报文字典（按ID和按名称索引）
        Args:
            file_name: Excel文件名
        Returns:
            (按ID索引, 按名称索引)的报文字典
        Raises:
            MatrixParseError: 解析错误
            FileNotFoundError: 文件不存在
        """
        if file_name in self._xlsx_cache:
            return self._xlsx_cache[file_name]

        file_path = os.path.join(self._matrix_dir, file_name)
        if not os.path.exists(file_path):
            logger.error(f"Excel文件不存在: {file_path}")
            raise FileNotFoundError(f"Excel文件不存在: {file_name}")

        try:
            wb = load_workbook(file_path, read_only=True, data_only=True)
            if "Matrix" not in wb.sheetnames:
                raise MatrixParseError("未找到名为'Matrix'的工作表")
            ws = wb["Matrix"]
            rows = list(ws.iter_rows(values_only=True))
            if len(rows) < 3:
                raise MatrixParseError("Matrix工作表内容不足")
            # AA1为当前节点名
            node_name = ws["AA1"].value or ""
            # 跳过表头和空行
            data_rows = rows[2:]
            messages_by_id: Dict[str, Message] = {}
            messages_by_name: Dict[str, Message] = {}
            current_msg = None
            for row in data_rows:
                # 跳过全空行
                if not any(row):
                    continue
                # A列非空，视为新报文
                if row[0]:
                    # 组装报文属性
                    current_msg = Message(
                        msg_name=str(row[0]),
                        msg_type=str(row[1]) if row[1] else "",
                        msg_id=str(row[2]) if row[2] else "",
                        msg_send_type=str(row[3]) if row[3] else "",
                        msg_cycle_time=str(row[4]) if row[4] else "",
                        frame_format=str(row[5]) if row[5] else "",
                        msg_length=int(row[6]) if row[6] else 0,
                        msg_cycle_time_fast=str(row[23]) if len(
                            row) > 23 and row[23] else "",
                        msg_nrof_reption=str(row[24]) if len(
                            row) > 24 and row[24] else "",
                        msg_delay_time=str(row[25]) if len(
                            row) > 25 and row[25] else "",
                        node_role=str(row[26]) if len(
                            row) > 26 and row[26] else "",
                        comment=str(row[27]) if len(
                            row) > 27 and row[27] else "",
                        signals=[]
                    )
                    messages_by_id[current_msg.msg_id] = current_msg
                    messages_by_name[current_msg.msg_name] = current_msg
                # H列非空，视为信号
                if row[7] and current_msg:
                    value_desc = str(row[22]) if row[22] else ""
                    value_desc = value_desc.replace("\n", ",")
                    signal = Signal(
                        signal_name=str(row[7]),
                        signal_description=str(row[8]) if row[8] else "",
                        byte_order=str(row[9]) if row[9] else "",
                        start_bit=int(row[10]) if row[10] else 0,
                        signal_send_type=str(row[11]) if row[11] else "",
                        bit_length=int(row[12]) if row[12] else 0,
                        data_type=str(row[13]) if row[13] else "",
                        resolution=float(row[14]) if row[14] else 0.0,
                        offset=float(row[15]) if row[15] else 0.0,
                        min_value=float(row[16]) if row[16] else None,
                        max_value=float(row[17]) if row[17] else None,
                        initial_value=str(row[18]) if row[18] else "",
                        invalid_value=str(row[19]) if row[19] else "",
                        inactive_value=str(row[20]) if row[20] else "",
                        unit=str(row[21]) if row[21] else "",
                        value_description=value_desc,
                        msg_id=current_msg.msg_id,
                        msg_name=current_msg.msg_name,
                        msg_type=current_msg.msg_type,
                        msg_send_type=current_msg.msg_send_type,
                        msg_cycle_time=current_msg.msg_cycle_time,
                        frame_format=current_msg.frame_format,
                        msg_length=current_msg.msg_length,
                        msg_cycle_time_fast=current_msg.msg_cycle_time_fast,
                        msg_nrof_reption=current_msg.msg_nrof_reption,
                        msg_delay_time=current_msg.msg_delay_time,
                        node_role=current_msg.node_role,
                        comment=current_msg.comment
                    )
                    current_msg.signals.append(signal)
            self._xlsx_cache[file_name] = (messages_by_id, messages_by_name)
            # 开发环境下自动保存JSON
            if get_app_env() == "dev":
                self._save_to_json(file_name, messages_by_id)
            return messages_by_id, messages_by_name
        except Exception as e:
            logger.error(f"解析Excel文件失败: {file_name}, 错误: {str(e)}")
            raise MatrixParseError(f"解析Excel文件失败: {str(e)}")

    def get_matrix_messages(self, net_type: str = "ICAN") -> Dict[str, Message]:
        """
        获取指定网络类型的所有报文（按报文ID索引）
        Args:
            net_type: 网络类型，"ICAN"或"ISCAN"
        Returns:
            Dict[str, Message]: 报文字典 {报文ID: Message对象}
        Raises:
            MatrixError: 矩阵相关错误
        """
        matrix_files = self._matrix_manager.get_current_matrix()
        file_name = matrix_files.get(net_type, "")
        if not file_name:
            logger.debug(f"尚未设置{net_type}矩阵文件")
            return {}
        if file_name in self._messages_cache:
            return self._messages_cache[file_name]
        messages_by_id, messages_by_name = self._parse_xlsx_file(file_name)
        self._messages_cache[file_name] = messages_by_id
        self._messages_by_name_cache[file_name] = messages_by_name
        return messages_by_id

    def get_message_by_id(self, message_id: str, net_type: str = "ICAN") -> Optional[Message]:
        """
        根据报文ID获取报文信息
        Args:
            message_id: 报文ID（字符串）
            net_type: 网络类型，"ICAN"或"ISCAN"
        Returns:
            Optional[Message]: 报文对象，不存在则返回None
        """
        try:
            messages = self.get_matrix_messages(net_type)
            return messages.get(str(message_id))
        except Exception as e:
            logger.error(f"根据ID获取报文信息失败: {str(e)}")
            return None

    def get_message_by_name(self, message_name: str, net_type: str = "ICAN") -> Optional[Message]:
        """
        根据报文名称获取报文信息
        Args:
            message_name: 报文名称
            net_type: 网络类型，"ICAN"或"ISCAN"
        Returns:
            Optional[Message]: 报文对象，不存在则返回None
        """
        try:
            matrix_files = self._matrix_manager.get_current_matrix()
            file_name = matrix_files.get(net_type, "")
            if not file_name:
                logger.warning(f"尚未设置{net_type}矩阵文件")
                return None
            if file_name not in self._messages_by_name_cache:
                self.get_matrix_messages(net_type)
            if file_name in self._messages_by_name_cache:
                return self._messages_by_name_cache[file_name].get(str(message_name))
            return None
        except Exception as e:
            logger.error(f"根据名称获取报文信息失败: {str(e)}")
            return None

    def clear_cache(self) -> None:
        """清空所有缓存数据"""
        self._xlsx_cache.clear()
        self._messages_cache.clear()
        self._messages_by_name_cache.clear()
        logger.info("已清空矩阵解析器缓存")
