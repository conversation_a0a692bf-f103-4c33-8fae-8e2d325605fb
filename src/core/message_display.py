"""
消息显示管理器
"""
from typing import Dict, Any, List
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread

class MessageDisplayManager(QObject):
    # 信号定义
    message_added = pyqtSignal(dict)  # 消息添加信号
    counters_updated = pyqtSignal(int, int)  # 计数器更新信号(tx_cnt, rx_cnt)
    counters_reset = pyqtSignal()  # 计数器重置信号
    
    def __init__(self):
        super().__init__()
        self._messages: List[Dict[str, Any]] = []  # 消息列表
        self._tx_cnt = 0  # 发送计数
        self._rx_cnt = 0  # 接收计数
        self._update_timer = QTimer()
        self._update_timer.moveToThread(QThread.currentThread())  # 确保定时器在主线程中运行
        self._update_timer.timeout.connect(self._update_display)
        self._update_timer.setInterval(100)  # 100ms更新一次
        
    def start_update(self):
        """开始更新显示"""
        if not self._update_timer.isActive():
            self._update_timer.start()
            
    def stop_update(self):
        """停止更新显示"""
        if self._update_timer.isActive():
            self._update_timer.stop()
            
    def _update_display(self):
        """更新显示"""
        # 在这里实现具体的更新逻辑
        pass
        
    def add_message(self, msg_dict: Dict[str, Any]):
        """添加消息
        
        Args:
            msg_dict: 消息字典
        """
        # 添加时间戳
        msg_dict["timestamp"] = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        # 更新计数器
        if msg_dict["direction"] == "发送":
            self._tx_cnt += 1
        else:
            self._rx_cnt += 1
            
        # 保存消息
        self._messages.append(msg_dict)
        
        # 发送消息添加信号
        self.message_added.emit(msg_dict)
        
        # 发送计数器更新信号
        self.counters_updated.emit(self._tx_cnt, self._rx_cnt)
        
    def reset_counters(self):
        """重置计数器"""
        self._tx_cnt = 0
        self._rx_cnt = 0
        self.counters_reset.emit()
        
    def clear_messages(self):
        """清空消息"""
        self._messages.clear()
        self.reset_counters()
        
    def get_messages(self) -> List[Dict[str, Any]]:
        """获取所有消息
        
        Returns:
            List[Dict[str, Any]]: 消息列表
        """
        return self._messages.copy()
        
    def get_counters(self) -> tuple[int, int]:
        """获取计数器值
        
        Returns:
            tuple[int, int]: (tx_cnt, rx_cnt)
        """
        return self._tx_cnt, self._rx_cnt 