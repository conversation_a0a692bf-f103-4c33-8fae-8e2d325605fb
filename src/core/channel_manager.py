"""
通道管理器
"""
from typing import Dict, <PERSON><PERSON>, Optional
from PyQt5.QtCore import QObject, pyqtSignal


class ChannelManager(QObject):
    # 信号定义
    # 通道状态改变信号(dev_idx, chn_idx, is_open)
    channel_state_changed = pyqtSignal(int, int, bool)
    protocol_changed = pyqtSignal(int, int)  # 协议类型改变信号(channel, protocol_type)

    def __init__(self):
        super().__init__()
        # {(dev_idx, chn_idx): protocol_type}
        self._opened_channels: Dict[Tuple[int, int], int] = {}

    def update_channel_state(self, dev_idx: int, chn_idx: int, is_open: bool, protocol_type: Optional[int] = None):
        """更新通道状态

        Args:
            dev_idx: 设备索引 (始终为0)
            chn_idx: 通道索引
            is_open: 是否打开
            protocol_type: 协议类型(0:CAN, 1:CANFD, 2:CANFD BRS)
        """
        # 确保设备索引为0
        dev_idx = 0
        channel_key = (dev_idx, chn_idx)

        if is_open:
            if protocol_type is not None:
                self._opened_channels[channel_key] = protocol_type
        else:
            if channel_key in self._opened_channels:
                del self._opened_channels[channel_key]

        # 发送通道状态改变信号
        self.channel_state_changed.emit(dev_idx, chn_idx, is_open)

    def get_protocol_type(self, dev_idx: int, chn_idx: int) -> Optional[int]:
        """获取通道协议类型

        Args:
            dev_idx: 设备索引 (始终为0)
            chn_idx: 通道索引

        Returns:
            Optional[int]: 协议类型,如果通道未打开则返回None
        """
        # 确保设备索引为0
        dev_idx = 0
        return self._opened_channels.get((dev_idx, chn_idx))

    def is_channel_open(self, dev_idx: int, chn_idx: int) -> bool:
        """检查通道是否打开

        Args:
            dev_idx: 设备索引 (始终为0)
            chn_idx: 通道索引

        Returns:
            bool: 是否打开
        """
        # 确保设备索引为0
        dev_idx = 0
        return (dev_idx, chn_idx) in self._opened_channels

    def get_opened_channels(self) -> Dict[Tuple[int, int], int]:
        """获取所有已打开的通道

        Returns:
            Dict[Tuple[int, int], int]: 已打开的通道字典
        """
        return self._opened_channels.copy()

    def get_first_opened_channel(self) -> Optional[Tuple[int, int]]:
        """获取第一个打开的通道

        Returns:
            Optional[Tuple[int, int]]: (dev_idx, chn_idx)或None
        """
        if self._opened_channels:
            return next(iter(self._opened_channels))
        return None
