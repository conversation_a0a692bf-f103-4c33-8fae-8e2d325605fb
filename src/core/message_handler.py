"""
消息处理器
"""
from typing import List, Dict, Any
from api.zlgcan import *
from src.utils.frame_formatter import FrameFormatter
from src.utils.logger import get_logger

# 定义CAN帧ID相关常量和函数
CAN_EFF_FLAG = 0x80000000  # 扩展帧标志(31位)
CAN_RTR_FLAG = 0x40000000  # 远程帧标志(30位)
CAN_ERR_FLAG = 0x20000000  # 错误帧标志(29位)


def MAKE_CAN_ID(id, eff, rtr, err):
    """创建CAN ID

    Args:
        id: 基础ID
        eff: 是否为扩展帧
        rtr: 是否为远程帧
        err: 是否为错误帧

    Returns:
        int: 组合后的CAN ID
    """
    result = id
    if eff:
        result |= CAN_EFF_FLAG
    if rtr:
        result |= CAN_RTR_FLAG
    if err:
        result |= CAN_ERR_FLAG
    return result


class MessageHandler:
    @staticmethod
    def dlc_to_len(dlc: int) -> int:
        """DLC转换为实际数据长度

        Args:
            dlc: DLC值

        Returns:
            int: 实际数据长度
        """
        if dlc <= 8:
            return dlc
        elif dlc == 9:
            return 12
        elif dlc == 10:
            return 16
        elif dlc == 11:
            return 20
        elif dlc == 12:
            return 24
        elif dlc == 13:
            return 32
        elif dlc == 14:
            return 48
        else:
            return 64

    @staticmethod
    def can_msg_to_dict(msg, is_transmit: bool = True) -> Dict[str, Any]:
        """将CAN消息转换为字典格式

        Args:
            msg: CAN消息
            is_transmit: 是否为发送消息

        Returns:
            Dict: 消息字典
        """
        msg_dict = {
            'direction': '发送' if is_transmit else '接收',
            'id': f"{msg.can_id:X}",
            'dlc': msg.can_dlc,
            'data': ''
        }

        # 使用统一的帧信息格式化工具类
        msg_dict['info'] = FrameFormatter.get_frame_info(
            msg.eff,
            msg.rtr,
            False,  # 非CANFD
            False   # 非BRS
        )

        # 数据
        if not msg.rtr:
            msg_dict['data'] = ' '.join(
                [f'{msg.data[i]:02X}' for i in range(msg.can_dlc)])

        return msg_dict

    @staticmethod
    def canfd_msg_to_dict(msg, is_transmit: bool = True) -> Dict[str, Any]:
        """将CANFD消息转换为字典格式

        Args:
            msg: CANFD消息
            is_transmit: 是否为发送消息

        Returns:
            Dict: 消息字典
        """
        msg_dict = {
            'direction': '发送' if is_transmit else '接收',
            'id': f"{msg.can_id:X}",
            'dlc': msg.len,
            'data': ''
        }

        # 使用统一的帧信息格式化工具类
        msg_dict['info'] = FrameFormatter.get_frame_info(
            msg.eff,
            msg.rtr,
            True,  # 是CANFD
            msg.brs  # 是否BRS
        )

        # 数据
        if not msg.rtr:
            msg_dict['data'] = ' '.join(
                [f'{msg.data[i]:02X}' for i in range(msg.len)])

        return msg_dict

    @staticmethod
    def create_can_msg(msg_id: int, dlc: int, data: List[int],
                       is_ext: bool = False, is_rtr: bool = False,
                       transmit_type: int = 0) -> ZCAN_Transmit_Data:
        """创建CAN消息

        Args:
            msg_id: 消息ID
            dlc: 数据长度
            data: 数据列表
            is_ext: 是否为扩展帧
            is_rtr: 是否为远程帧
            transmit_type: 发送类型

        Returns:
            ZCAN_Transmit_Data: CAN消息
        """
        msg = ZCAN_Transmit_Data()

        # 设置ID和标志位
        msg.frame.can_id = MAKE_CAN_ID(msg_id, is_ext, is_rtr, False)

        # 设置数据长度
        msg.frame.can_dlc = dlc

        # 设置数据
        for i in range(min(dlc, len(data))):
            msg.frame.data[i] = data[i]

        # 设置发送类型
        msg.transmit_type = transmit_type

        return msg

    @staticmethod
    def create_canfd_msg(msg_id: int, dlc: int, data: List[int],
                         is_ext: bool = False, is_rtr: bool = False,
                         is_brs: bool = False, transmit_type: int = 0) -> ZCAN_TransmitFD_Data:
        """创建CANFD消息

        Args:
            msg_id: 消息ID
            dlc: 数据长度
            data: 数据列表
            is_ext: 是否为扩展帧
            is_rtr: 是否为远程帧
            is_brs: 是否启用位速率切换
            transmit_type: 发送类型

        Returns:
            ZCAN_TransmitFD_Data: CANFD消息
        """
        logger = get_logger("msg_handler")

        msg = ZCAN_TransmitFD_Data()

        # 使用MAKE_CAN_ID函数设置ID和标志位，与create_can_msg方法保持一致
        msg.frame.can_id = MAKE_CAN_ID(msg_id, is_ext, is_rtr, False)

        # 记录ID设置情况
        logger.debug(f"创建CANFD消息: ID=0x{msg_id:X}, 扩展帧={is_ext}, 远程帧={is_rtr}, "
                     f"BRS={is_brs}, 处理后ID=0x{msg.frame.can_id:X}")

        # 设置数据长度
        msg.frame.len = dlc

        # 设置BRS位 (位速率切换)
        msg.frame.brs = 1 if is_brs else 0
        logger.debug(f"设置BRS位: is_brs={is_brs}, brs={msg.frame.brs}")

        # 设置ESI位 (错误状态指示器)
        msg.frame.esi = 0  # 一般设为0

        # 设置数据
        for i in range(min(dlc, len(data))):
            msg.frame.data[i] = data[i]

        # 生成数据内容字符串用于日志
        data_str = ' '.join(
            [f"{msg.frame.data[i]:02X}" for i in range(min(8, dlc))])
        if dlc > 8:
            data_str += "..."

        # 设置发送类型
        msg.transmit_type = transmit_type

        logger.debug(f"CANFD消息创建完成: ID=0x{msg.frame.can_id:X}, 长度={dlc}, "
                     f"数据={data_str}, BRS={msg.frame.brs}, 发送类型={transmit_type}")

        return msg
