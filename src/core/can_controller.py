"""
CAN控制器核心类
"""
import json
import threading
import time
from typing import List, Tuple, Optional, Dict, Any, Union, Callable
from queue import Queue
from datetime import datetime
from ctypes import c_void_p
import traceback
import logging
import os
import random
import struct
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt, QObject, pyqtSlot
from src.utils.constants import *
from src.utils.paths import DEVICE_INFO_FILE
from src.utils.frame_formatter import FrameFormatter
from src.utils.logger import get_logger
from api.zlgcan import *
from src.core.receive_thread import MessageReceiveThread
from src.utils.can_utils import get_readable_baudrate, dlc_to_len
from src.core.device_manager import DeviceManager

# =========================
# 常量与类型定义
# =========================
CANFD_RESISTER = 0x42  # 终端电阻设置命令
DataType = Any  # 数据类型别名

logger = get_logger("can_controller")

# =========================
# CAN控制器主类
# =========================


class CANController(QObject):
    message_sent = pyqtSignal(bool, object)

    def __init__(self):
        super().__init__()
        self._zcan = ZCAN()
        self._device_manager = DeviceManager(self._zcan)
        self._can_handles: Dict[int, int] = {}
        self._chn_states: Dict[int, bool] = {}
        self._channel_info: Dict[int, Dict[str, Any]] = {}
        self._read_threads: Dict[int, MessageReceiveThread] = {}
        self._resistance_enabled = {}
        self._tx_cnts: Dict[int, int] = {}
        self._rx_cnts: Dict[int, int] = {}
        self._lock = threading.RLock()
        self._msg_received_callback: Optional[Callable[[
            Dict[str, Any]], None]] = None
        self._load_device_info()
        logger.info("CAN控制器初始化完成")

    # ========== 设备与通道管理 ==========
    def _load_device_info(self):
        """加载设备信息配置文件"""
        try:
            with open(DEVICE_INFO_FILE, "r") as fd:
                self._dev_info = json.load(fd)

            # 初始化波特率映射表
            self._baudrate_map = {}
            self._data_baudrate_map = {}

            # 遍历所有设备信息
            for dev_name, dev_info in self._dev_info.items():
                if "chn_info" in dev_info:
                    chn_info = dev_info["chn_info"]

                    # 处理标准波特率
                    if "baudrate" in chn_info:
                        for rate_name, timing_value in chn_info["baudrate"].items():
                            if isinstance(timing_value, int):
                                self._baudrate_map[timing_value] = rate_name
                            elif isinstance(timing_value, str) and timing_value.isdigit():
                                self._baudrate_map[int(
                                    timing_value)] = rate_name

                    # 处理数据段波特率
                    if "data_baudrate" in chn_info:
                        for rate_name, timing_value in chn_info["data_baudrate"].items():
                            if isinstance(timing_value, int):
                                self._data_baudrate_map[timing_value] = rate_name
                            elif isinstance(timing_value, str) and timing_value.isdigit():
                                self._data_baudrate_map[int(
                                    timing_value)] = rate_name

            # 记录加载的映射表信息
            logger.debug(f"标准波特率映射表: {', '.join([f'{timing}=>{rate}' for timing, rate in self._baudrate_map.items()])}")
            logger.debug(f"数据段波特率映射表: {', '.join([f'{timing}=>{rate}' for timing, rate in self._data_baudrate_map.items()])}")

            logger.debug(f"加载设备信息完成: {self._dev_info}")
        except Exception as e:
            logger.error(f"加载设备信息失败: {e}", exc_info=True)
            self._dev_info = {}
            self._baudrate_map = {}
            self._data_baudrate_map = {}

    def open_device(self, dev_type: int, dev_idx: int) -> bool:
        return self._device_manager.open_device(dev_type, dev_idx, self._dev_info)

    def close_device(self):
        self._device_manager.close_device()

    def get_device_info(self) -> Optional[ZCAN_DEVICE_INFO]:
        return self._device_manager.get_device_info()

    def is_device_opened(self, dev_idx: int) -> bool:
        return self._device_manager.is_device_opened(dev_idx)

    @property
    def _dev_handle(self):
        return self._device_manager.dev_handle

    @property
    def is_open(self) -> bool:
        return self._device_manager.is_open

    @property
    def _cur_dev_info(self):
        return self._device_manager.cur_dev_info

    @property
    def _is_canfd(self):
        return self._device_manager.is_canfd

    @property
    def _res_support(self):
        return self._device_manager.res_support

    @property
    def supports_resistance(self) -> bool:
        return self._device_manager.res_support

    def init_channel(self, chn_idx: int, chn_cfg: ZCAN_CHANNEL_INIT_CONFIG) -> bool:
        """
        初始化CAN通道

        Args:
            chn_idx: 通道索引
            chn_cfg: 通道配置

        Returns:
            bool: 是否初始化成功
        """
        try:
            with self._lock:
                if not self.is_open:
                    logger.error("设备未打开")
                    return False

                if chn_idx in self._can_handles:
                    # 如果通道已初始化，先关闭它
                    self.close_channel(chn_idx)

                # 记录初始配置类型
                # 注意：can_type是c_uint类型，需要获取其值进行比较
                can_type_value = chn_cfg.can_type.value if hasattr(
                    chn_cfg.can_type, 'value') else chn_cfg.can_type
                is_canfd = (can_type_value == ZCAN_TYPE_CANFD.value)
                channel_type = "CANFD" if is_canfd else "CAN"

                # 添加通道类型信息
                if chn_idx not in self._channel_info:
                    self._channel_info[chn_idx] = {}
                self._channel_info[chn_idx]['type'] = channel_type

                logger.info(
                    f"开始初始化通道{chn_idx} - 类型: {channel_type}, can_type值: {can_type_value}")

                # 记录详细的配置参数
                if not is_canfd:
                    # 标准CAN模式
                    timing0 = chn_cfg.config.can.timing0
                    timing1 = chn_cfg.config.can.timing1
                    filter_value = chn_cfg.config.can.filter
                    mode_value = chn_cfg.config.can.mode

                    # 从映射表中查找波特率名称
                    baudrate_name = "未知"
                    if timing0 in self._baudrate_map:
                        baudrate_name = self._baudrate_map[timing0]

                    # 保存波特率信息
                    self._channel_info[chn_idx]['baudrate'] = baudrate_name
                    self._channel_info[chn_idx]['timing0'] = timing0

                    # 记录原始配置参数
                    logger.info(f"通道{chn_idx}配置详情 - 类型:CAN, 波特率:{baudrate_name}(timing0={timing0}), "
                                f"filter:{filter_value}, mode:{mode_value}(0:正常,1:只听)")

                else:
                    # CANFD模式
                    abit_timing = chn_cfg.config.canfd.abit_timing
                    dbit_timing = chn_cfg.config.canfd.dbit_timing
                    brp = chn_cfg.config.canfd.brp
                    filter_value = chn_cfg.config.canfd.filter
                    mode_value = chn_cfg.config.canfd.mode

                    # 从映射表中查找波特率名称
                    abit_baudrate_name = "未知"
                    dbit_baudrate_name = "未知"

                    if abit_timing in self._baudrate_map:
                        abit_baudrate_name = self._baudrate_map[abit_timing]

                    if dbit_timing in self._data_baudrate_map:
                        dbit_baudrate_name = self._data_baudrate_map[dbit_timing]

                    # 保存波特率信息
                    self._channel_info[chn_idx]['abit_baudrate'] = abit_baudrate_name
                    self._channel_info[chn_idx]['dbit_baudrate'] = dbit_baudrate_name
                    self._channel_info[chn_idx]['abit_timing'] = abit_timing
                    self._channel_info[chn_idx]['dbit_timing'] = dbit_timing

                    # 记录原始配置参数
                    logger.info(f"通道{chn_idx}配置详情 - 类型:CANFD, "
                                f"仲裁段波特率:{abit_baudrate_name}(timing={abit_timing}), "
                                f"数据段波特率:{dbit_baudrate_name}(timing={dbit_timing}), "
                                f"brp:{brp}, filter:{filter_value}, mode:{mode_value}(0:正常,1:只听)")

                # 初始化通道前记录
                logger.info(f"正在调用ZCAN_InitCAN初始化通道{chn_idx}...")

                # 初始化通道
                can_handle = self._zcan.InitCAN(
                    self._dev_handle, chn_idx, chn_cfg)
                if can_handle == INVALID_CHANNEL_HANDLE:
                    logger.error(f"通道{chn_idx}初始化失败，ZCAN_InitCAN返回无效句柄")
                    return False

                # 启动通道前记录
                logger.info(f"正在调用ZCAN_StartCAN启动通道{chn_idx}...")

                # 启动通道
                start_result = self._zcan.StartCAN(can_handle)
                if start_result != ZCAN_STATUS_OK:
                    logger.error(
                        f"通道{chn_idx}启动失败，ZCAN_StartCAN返回状态码:{start_result}")
                    return False

                logger.info(f"通道{chn_idx}启动成功")

                # 设置终端电阻
                resistance_enabled = False
                if self._res_support:
                    # 从两个可能的位置获取终端电阻状态
                    pending_key = 'pending_' + str(chn_idx)
                    if pending_key in self._resistance_enabled:
                        # 优先使用pending状态（用户刚刚设置的状态）
                        resistance_enabled = self._resistance_enabled[pending_key]
                        # 应用后删除pending状态
                        del self._resistance_enabled[pending_key]
                    else:
                        # 否则使用之前保存的状态
                        resistance_enabled = self._resistance_enabled.get(
                            chn_idx, False)

                    # 保存最终状态
                    self._resistance_enabled[chn_idx] = resistance_enabled
                    self._channel_info[chn_idx]['resistance'] = resistance_enabled

                    logger.info(
                        f"正在设置通道{chn_idx}终端电阻: {'启用' if resistance_enabled else '禁用'}")

                    ip = self._zcan.GetIProperty(self._dev_handle)
                    if ip:
                        value = "1" if resistance_enabled else "0"
                        ret = self._zcan.SetValue(
                            ip, f"{chn_idx}/initenal_resistance", value)
                        if ret != ZCAN_STATUS_OK:
                            logger.warning(f"设置通道{chn_idx}终端电阻失败，返回状态码:{ret}")
                        else:
                            logger.info(
                                f"通道{chn_idx}终端电阻设置成功: {'启用' if resistance_enabled else '禁用'}")
                        self._zcan.ReleaseIProperty(ip)
                    else:
                        logger.warning(f"获取设备属性接口失败，无法设置通道{chn_idx}终端电阻")

                self._can_handles[chn_idx] = can_handle
                self._chn_states[chn_idx] = True

                # 输出最终配置总结 - 包含波特率名称和timing值
                if not is_canfd:
                    logger.info(f"通道{chn_idx}初始化成功 - 类型:CAN, 波特率:{self._channel_info[chn_idx]['baudrate']}(timing0={timing0}), "
                                f"模式:{'只听' if mode_value else '正常'}, 终端电阻:{'启用' if resistance_enabled else '禁用'}")
                else:
                    logger.info(f"通道{chn_idx}初始化成功 - 类型:CANFD, "
                                f"仲裁段波特率:{self._channel_info[chn_idx]['abit_baudrate']}(timing={abit_timing}), "
                                f"数据段波特率:{self._channel_info[chn_idx]['dbit_baudrate']}(timing={dbit_timing}), "
                                f"模式:{'只听' if mode_value else '正常'}, 终端电阻:{'启用' if resistance_enabled else '禁用'}")

                return True

        except Exception as e:
            logger.error(f"初始化通道{chn_idx}失败: {e}", exc_info=True)
            return False

    def close_channel(self, chn_idx: int):
        """关闭CAN通道"""
        try:
            with self._lock:
                if not self._chn_states.get(chn_idx, False):
                    return

                logger.info(f"正在关闭通道{chn_idx}")

                # 停止接收线程
                if chn_idx in self._read_threads:
                    self._read_threads[chn_idx].stop()
                    del self._read_threads[chn_idx]

                # 重置通道
                if chn_idx in self._can_handles:
                    self._zcan.ResetCAN(self._can_handles[chn_idx])
                    del self._can_handles[chn_idx]

                # 更新状态
                self._chn_states[chn_idx] = False
                logger.info(f"通道{chn_idx}关闭完成")
        except Exception as e:
            logger.error(f"关闭通道{chn_idx}失败: {e}", exc_info=True)

    def start_receive(self, chn_idx: int):
        """启动通道接收线程"""
        try:
            with self._lock:
                if not self._chn_states.get(chn_idx, False):
                    logger.warning(f"通道{chn_idx}未打开，无法启动接收线程")
                    return

                if chn_idx in self._read_threads:
                    logger.warning(f"通道{chn_idx}接收线程已存在")
                    return

            logger.info(f"正在启动通道{chn_idx}接收线程")
            thread = MessageReceiveThread(
                self._can_handles[chn_idx],
                self._zcan,
                self._cur_dev_info,
                chn_idx,
                self._msg_received_callback
            )
            thread.message_received.connect(self._handle_received_messages)
            thread.start()
            self._read_threads[chn_idx] = thread
            logger.info(f"通道{chn_idx}接收线程启动完成")
        except Exception as e:
            logger.error(f"启动通道{chn_idx}接收线程失败: {e}", exc_info=True)

    @pyqtSlot(list)
    def _handle_received_messages(self, messages):
        if self._msg_received_callback:
            with self._lock:
                for msg in messages:
                    try:
                        chn_idx = msg["channel"]
                        if chn_idx not in self._rx_cnts:
                            self._rx_cnts[chn_idx] = 0
                        self._rx_cnts[chn_idx] += 1
                        self._msg_received_callback(msg)
                    except Exception as e:
                        logger.error(f"消息处理回调异常: {e}", exc_info=True)

    def set_msg_received_callback(self, callback):
        self._msg_received_callback = callback
        for thread in self._read_threads.values():
            thread._msg_received_callback = callback

    # ========== 消息收发 ==========
    def transmit_msg(self, msg, is_canfd: bool, chn_idx: int) -> int:
        if not self._chn_states.get(chn_idx, False):
            logger.warning(f"通道{chn_idx}未打开，无法发送消息")
            return 0
        try:
            with self._lock:
                if is_canfd:
                    is_brs = bool(msg.frame.brs)
                    logger.info(
                        f"正在发送CANFD消息: ID=0x{msg.frame.can_id:X}, 长度={msg.frame.len}, BRS={'启用' if is_brs else '禁用'}, 通道={chn_idx}")
                    ret = self._zcan.TransmitFD(
                        self._can_handles[chn_idx], msg, 1)
                    if ret <= 0:
                        logger.error(
                            f"CANFD消息发送失败: ID=0x{msg.frame.can_id:X}, 通道={chn_idx}, BRS={'启用' if is_brs else '禁用'}, 返回值={ret}")
                else:
                    logger.info(
                        f"正在发送CAN消息: ID=0x{msg.frame.can_id:X}, 长度={msg.frame.can_dlc}, 通道={chn_idx}")
                    ret = self._zcan.Transmit(
                        self._can_handles[chn_idx], msg, 1)
                    if ret <= 0:
                        logger.error(
                            f"CAN消息发送失败: ID=0x{msg.frame.can_id:X}, 通道={chn_idx}, 返回值={ret}")
                if ret > 0:
                    if chn_idx not in self._tx_cnts:
                        self._tx_cnts[chn_idx] = 0
                    self._tx_cnts[chn_idx] += ret
                else:
                    logger.error(
                        f"通道{chn_idx}发送消息失败: ID=0x{msg.frame.can_id:X}")
                return ret
        except Exception as e:
            logger.error(f"通道{chn_idx}发送消息异常: {e}", exc_info=True)
            return 0

    def send_message(self, dev_idx: int, chn_idx: int, msg_info: Dict[str, Any]) -> bool:
        try:
            can_type = msg_info.get("can_type", "CAN")
            is_canfd = msg_info.get("is_canfd", False)
            is_brs = bool(msg_info.get("flags", 0) & 0x4)
            data_hex = [f"0x{x:02X}" for x in msg_info["data"]] if isinstance(
                msg_info["data"], list) else msg_info["data"]
            msg_id = msg_info["id"]
            if isinstance(msg_id, str):
                if msg_id.startswith('0x'):
                    msg_id = int(msg_id, 16)
                else:
                    try:
                        msg_id = int(msg_id, 16)
                    except ValueError:
                        msg_id = int(msg_id)
            transmit_type = 2 if msg_info.get("send_type") == "自发自收" else 0
            if is_canfd:
                msg = self._create_canfd_msg(
                    msg_id=msg_id,
                    dlc=msg_info["len"],
                    data=msg_info["data"],
                    is_ext=bool(msg_info["flags"] & 0x1),
                    is_rtr=bool(msg_info["flags"] & 0x2),
                    is_brs=is_brs,
                    transmit_type=transmit_type
                )
                ret = self._zcan.TransmitFD(self._can_handles[chn_idx], msg, 1)
            else:
                msg = self._create_can_msg(
                    msg_id=msg_id,
                    dlc=msg_info["len"],
                    data=msg_info["data"],
                    is_ext=bool(msg_info["flags"] & 0x1),
                    is_rtr=bool(msg_info["flags"] & 0x2),
                    transmit_type=transmit_type
                )
                ret = self._zcan.Transmit(self._can_handles[chn_idx], msg, 1)
            success = ret > 0
            if success:
                logger.info(f"消息发送成功: ID=0x{msg_id:X}, 通道={chn_idx}")
            else:
                logger.error(
                    f"消息发送失败: ID=0x{msg_id:X}, 通道={chn_idx}, 错误码={ret}")
            self.message_sent.emit(success, msg_info)
            if success and self._msg_received_callback:
                display_msg = {
                    "device": dev_idx,
                    "channel": chn_idx,
                    "id": f"{msg_id:03X}",
                    "direction": "发送",
                    "info": FrameFormatter.get_frame_info(
                        bool(msg_info["flags"] & 0x1),
                        bool(msg_info["flags"] & 0x2),
                        is_canfd,
                        is_brs
                    ),
                    "dlc": msg_info["len"],
                    "data": ' '.join([f"{b:02X}" for b in msg_info["data"]]) if isinstance(msg_info["data"], list) else msg_info["data"],
                    "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3]
                }
                try:
                    self._msg_received_callback(display_msg)
                except Exception as e:
                    logger.error(f"添加发送消息到显示列表异常: {e}", exc_info=True)
            return success
        except Exception as e:
            logger.error(f"发送消息异常: {str(e)}\n{traceback.format_exc()}")
            self.message_sent.emit(False, msg_info)
            return False

    # ========== 计数器与状态 ==========
    def get_tx_count(self, chn_idx: int) -> int:
        with self._lock:
            return self._tx_cnts.get(chn_idx, 0)

    def get_rx_count(self, chn_idx: int) -> int:
        with self._lock:
            return self._rx_cnts.get(chn_idx, 0)

    def reset_counters(self, chn_idx: int):
        with self._lock:
            self._tx_cnts[chn_idx] = 0
            self._rx_cnts[chn_idx] = 0

    def is_channel_open(self, chn_idx: int) -> bool:
        return self._chn_states.get(chn_idx, False)

    def set_resistance(self, chn_idx: int, enabled: bool) -> bool:
        """
        设置并立即应用通道终端电阻状态

        Args:
            chn_idx: 通道索引
            enabled: 是否启用终端电阻

        Returns:
            bool: 是否设置成功
        """
        if not self.is_open or not self._chn_states.get(chn_idx, False):
            logger.warning(f"设置终端电阻失败: 设备未打开或通道未初始化")
            return False
        if not self._res_support:
            logger.warning(f"当前设备不支持设置终端电阻")
            return False

        # 记录旧状态
        old_state = "启用" if self._resistance_enabled.get(
            chn_idx, False) else "禁用"
        new_state = "启用" if enabled else "禁用"
        logger.info(f"正在设置通道{chn_idx}终端电阻: {old_state} -> {new_state}")

        try:
            iproperty = self._zcan.GetIProperty(self._dev_handle)
            if not iproperty:
                logger.error(f"获取设备IProperty接口失败")
                return False
            value = "1" if enabled else "0"
            path = f"{chn_idx}/initenal_resistance"
            result = self._zcan.SetValue(iproperty, path, value)
            self._zcan.ReleaseIProperty(iproperty)
            if result != ZCAN_STATUS_OK:
                logger.error(f"设置通道{chn_idx}终端电阻失败: 状态码={result}")
                return False

            # 更新保存的电阻状态
            self._resistance_enabled[chn_idx] = enabled
            logger.info(f"通道{chn_idx}终端电阻设置成功: {new_state}")
            return True
        except Exception as e:
            logger.error(f"设置通道{chn_idx}终端电阻异常: {e}", exc_info=True)
            return False

    def set_pending_resistance(self, chn_idx: int, enabled: bool):
        """
        设置将要应用的终端电阻状态（用于在通道打开时设置）

        Args:
            chn_idx: 通道索引
            enabled: 是否启用终端电阻
        """
        pending_key = 'pending_' + str(chn_idx)
        prev_state = "未设置"
        if pending_key in self._resistance_enabled:
            prev_state = "启用" if self._resistance_enabled[pending_key] else "禁用"

        # 设置待处理的终端电阻状态
        self._resistance_enabled[pending_key] = enabled

        logger.info(
            f"设置通道{chn_idx}终端电阻待处理状态: {prev_state} -> {'启用' if enabled else '禁用'}")

    def is_channel_opened(self, dev_idx: int, chn_idx: int) -> bool:
        return chn_idx in self._can_handles and self._chn_states.get(chn_idx, False)

    # ========== 私有工具方法 ==========
    def _create_can_msg(self, msg_id: int, dlc: int, data: DataType, is_ext: bool = False,
                        is_rtr: bool = False, transmit_type: int = 0) -> ZCAN_Transmit_Data:
        data_bytes = []
        if data and not is_rtr:
            if isinstance(data, str):
                data_items = data.split()
                for i in range(min(len(data_items), dlc)):
                    data_bytes.append(int(data_items[i], 16))
            elif isinstance(data, list):
                data_bytes = data[:dlc]
        while len(data_bytes) < dlc:
            data_bytes.append(0)
        msg = ZCAN_Transmit_Data()
        msg.frame.can_id = msg_id | (0x80000000 if is_ext else 0)
        msg.frame.can_dlc = dlc
        msg.frame.rtr = 1 if is_rtr else 0
        msg.frame.eff = 1 if is_ext else 0
        for i in range(dlc):
            msg.frame.data[i] = data_bytes[i] if i < len(data_bytes) else 0
        msg.transmit_type = transmit_type
        return msg

    def _create_canfd_msg(self, msg_id: int, dlc: int, data: DataType, is_ext: bool = False,
                          is_rtr: bool = False, is_brs: bool = False,
                          transmit_type: int = 0) -> ZCAN_TransmitFD_Data:
        msg = ZCAN_TransmitFD_Data()
        msg.frame.can_id = msg_id
        msg.frame.eff = 1 if is_ext else 0
        msg.frame.rtr = 1 if is_rtr else 0
        msg.frame.err = 0
        msg.frame.len = dlc_to_len(dlc)
        msg.frame.brs = 1 if is_brs else 0
        msg.frame.esi = 0
        msg.frame.__res = 0
        msg.frame.__res0 = 0
        msg.frame.__res1 = 0
        data_bytes = []
        if isinstance(data, str):
            data_str = data.strip()
            if data_str:
                parts = data_str.split()
                for part in parts:
                    data_bytes.append(int(part, 16))
            else:
                data_bytes = [0] * msg.frame.len
        else:
            data_bytes = data
        data_length = min(len(data_bytes), msg.frame.len)
        for i in range(data_length):
            msg.frame.data[i] = data_bytes[i]
        msg.transmit_type = transmit_type
        return msg
