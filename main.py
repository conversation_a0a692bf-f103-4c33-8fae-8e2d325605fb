"""
主程序入口
"""
import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from src.ui.main_window import MainWindow
from src.utils.font_manager import font_manager
from src.utils.icon_manager import icon_manager
from src.utils.env_check import check_all_env

# 必须在QApplication创建前设置全局属性
QApplication.setAttribute(Qt.AA_DisableHighDpiScaling)
QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)


def main():
    app = QApplication(sys.argv)  # 必须最先创建
    check_all_env()               # 然后再做环境检查
    # 设置高DPI缩放
    os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "0"  # 修改为0，禁用自动缩放
    os.environ["QT_SCALE_FACTOR"] = "1"  # 显式设置缩放因子为1
    # 添加额外的DPI设置
    os.environ["QT_DEVICE_PIXEL_RATIO"] = "1"
    os.environ["QT_FONT_DPI"] = "96"  # 使用标准DPI

    # 初始化字体管理器
    font_manager.initialize()

    # 初始化图标管理器
    icon_manager.initialize()

    # 设置应用程序图标
    if icon_manager.icon:
        app.setWindowIcon(icon_manager.icon)

    window = MainWindow()
    window.show()
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
