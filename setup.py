import os
import sys
from setuptools import setup, find_packages

# 读取 requirements.txt 并过滤注释/空行
with open('requirements.txt', encoding='utf-8') as f:
    required = [line.strip() for line in f if line.strip()
                and not line.strip().startswith('#')]

# Windows 平台专属依赖
if sys.platform.startswith('win'):
    required.append('pywin32>=300')

setup(
    name="zlgcan_simulation",
    version="1.0",
    author="常孝强",
    author_email="<EMAIL>",
    description="ZLG CAN/CANFD 测试工具",
    long_description=open("README.md", encoding="utf-8").read(),
    long_description_content_type="text/markdown",
    url="https://www.zlg.cn",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: Other/Proprietary License",
        "Operating System :: Microsoft :: Windows",
    ],
    python_requires=">=3.10",
    install_requires=required,
    entry_points={
        'console_scripts': [
            'zlgcan-test=main:main',
        ],
    },
    include_package_data=True,
    package_data={
        '': [
            'api/*.dll', 'api/*.json', 'api/*.lib', 'api/*.h',
            'api/zlgcan_x86/*.dll', 'api/zlgcan_x64/*.dll',
            'api/kerneldlls/*.dll', 'api/kerneldlls/*.ini', 'api/kerneldlls/*.xml',
            'api/kerneldlls/devices_property/*.xml', 'api/kerneldlls/devices_property/*.ini',
            'api/kerneldlls/devices_property/default/*',
            'resource/*', 'config/*', 'logs/*'
        ],
    },
    data_files=[
        ('config', []),
        ('logs', []),
    ],
)

# 安装后输出友好提示


def print_post_install_message():
    print("\n" + "="*80)
    print("ZLG CAN 测试工具安装完成!")
    print("使用方法:")
    print("1. 通过命令行: zlgcan-test")
    print("2. 或者直接运行: python main.py")
    print("请确保您的ZLG CAN设备已正确连接并安装驱动。")
    print("="*80 + "\n")


if len(sys.argv) > 1 and sys.argv[1].lower() == 'install':
    print_post_install_message()
