# PyZLG_CAN 项目文件目录结构

## 项目概述

PyZLG_CAN 是一个基于 Python 和 PyQt5 的 ZLGCAN 仿真程序，用于周立功 CAN 设备的通信和仿真。本文档详细说明项目的文件目录结构，帮助开发者更好地理解和维护代码。

## 目录结构总览

```
project_root/
├── api/                   # ZLG CAN设备API接口文件
├── backup/                # 备份文件目录
├── config/                # 配置文件目录
├── docs/                  # 文档目录
├── logs/                  # 日志文件目录
├── matrix/                # 矩阵文件目录
├── resource/              # 资源文件目录
├── src/                   # 源代码目录
│   ├── core/              # 核心功能模块
│   ├── ui/                # 用户界面模块
│   └── utils/             # 工具类模块
├── test/                  # 测试脚本目录
├── test_reports/          # 测试报告目录
├── main.py                # 程序入口文件
├── venv_setup.py          # 虚拟环境设置脚本
├── requirements.txt       # 依赖库列表
├── README.md              # 项目说明文档
└── setup.py               # 打包脚本
```

## 目录详细说明

### 1. `api/` - ZLG CAN 设备 API 接口文件

```
api/
├── kerneldlls/           # 核心DLL文件
├── zlgcan.py             # Python封装的ZLG CAN接口
├── dev_info.json         # 设备信息配置文件
├── zlgcan.dll            # ZLG CAN动态链接库
├── zlgcan.lib            # ZLG CAN链接库
├── zlgcan.h              # ZLG CAN头文件
├── canframe.h            # CAN帧结构定义头文件
├── config.h              # 配置相关头文件
├── typedef.h             # 类型定义头文件
└── __init__.py           # 包初始化文件
```

### 2. `backup/` - 备份文件目录

```
backup/
├── api_backups/          # API文件备份
└── libs/                 # 库文件备份
```

### 3. `config/` - 配置文件目录

```
config/
├── panel_designs/        # 面板设计配置
├── api_history.json      # API更新历史记录
├── channel_config.json   # 通道配置
├── lib_history.json      # 库更新历史记录
├── message_panel.json    # 消息面板配置
├── tabs_config.json      # 标签页配置
├── ui_settings.json      # UI设置
└── window_layout.json    # 窗口布局配置
```

### 4. `docs/` - 文档目录

```
docs/
├── 0-项目架构与功能总结.md             # 项目架构总览
├── 1-CAN设备管理设计详解.md           # CAN设备管理文档
├── 2-自定义发送报文功能详解.md        # 发送报文功能文档
├── 3-CAN设备通道管理与消息显示架构.md  # 通道与消息显示文档
├── 4-矩阵文件管理功能详解.md          # 矩阵文件管理文档
├── 5-消息发送方式对比.md              # 消息发送方式文档
├── 6-进制转换功能说明.md              # 进制转换功能文档
└── 项目文件目录结构.md                # 本文档
```

### 5. `logs/` - 日志文件目录

```
logs/
└── app_YYYYMMDD.log      # 按日期命名的应用日志文件
```

### 6. `matrix/` - 矩阵文件目录

用于存储 CAN 矩阵文件，支持多种格式的矩阵文件导入。

### 7. `resource/` - 资源文件目录

```
resource/
├── calculator.ico        # 计算器图标
├── icon.ico              # 应用程序图标(ICO格式)
├── icon.png              # 应用程序图标(PNG格式)
└── icon.txt              # 图标相关说明
```

### 8. `src/` - 源代码目录

#### 8.1 `src/core/` - 核心功能模块

```
src/core/
├── can_controller.py          # CAN控制器核心类
├── channel_manager.py         # 通道管理器
├── config_manager.py          # 配置管理器
├── dbc_message_sender.py      # DBC消息发送器
├── lib_manager.py             # 库管理器
├── matrix_manager.py          # 矩阵管理器
├── matrix_message_encoder.py  # 矩阵消息编码器
├── matrix_parser.py           # 矩阵文件解析器
├── message_display.py         # 消息显示管理
├── message_filter.py          # 消息过滤器
├── message_handler.py         # 消息处理器
└── message_sender.py          # 消息发送器
```

#### 8.2 `src/ui/` - 用户界面模块

```
src/ui/
├── dialogs/                   # 对话框组件
├── widgets/                   # 自定义控件
├── main_window.py             # 主窗口
└── tab_manager.py             # 标签页管理器
```

#### 8.3 `src/utils/` - 工具类模块

```
src/utils/
├── app_info.py                # 应用信息
├── constants.py               # 常量定义
├── font_manager.py            # 字体管理器
├── frame_formatter.py         # 帧格式化
├── icon_manager.py            # 图标管理器
├── logger.py                  # 日志工具
├── msg_hex_calculator.py      # 报文十六进制计算器
└── paths.py                   # 路径管理
```

### 9. `test/` - 测试脚本目录

包含用于测试各模块功能的脚本文件。

### 10. `test_reports/` - 测试报告目录

存储测试执行结果和报告。

### 11. 根目录文件

- `main.py` - 程序入口文件
- `venv_setup.py` - 虚拟环境设置脚本
- `requirements.txt` - 项目依赖库列表
- `README.md` - 项目说明文档
- `setup.py` - 项目打包脚本
- `.gitignore` - Git 忽略文件配置

## 文件路径管理

项目中的文件路径管理主要由`src/utils/paths.py`模块统一管理，确保在不同环境下路径的一致性和正确性。

## 编码规范

项目遵循 Python PEP 8 编码规范，采用模块化设计，各模块职责明确，具有良好的可维护性和可扩展性。
