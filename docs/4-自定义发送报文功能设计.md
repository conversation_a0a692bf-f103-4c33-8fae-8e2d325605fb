# 自定义发送报文功能详解

## 自定义发送报文的核心组成部分

### 1. 用户界面层 (UI 层)

- **`MessagePanel`类**：提供用户交互界面，允许用户设置和控制报文发送
  - 包含设置 CAN ID、数据、帧类型、发送次数、发送周期等参数的 UI 控件
  - 提供"开始自定义发送"和"停止自定义发送"功能
  - 发送状态反馈和错误处理

### 2. 业务逻辑层

- **`MessageSender`类**：消息发送管理器，负责管理发送线程和发送状态
  - 控制发送任务的开始和停止
  - 管理消息发送的生命周期
  - 提供错误处理和事件通知
- **`MessageSendThread`类**：实际执行发送操作的线程
  - 管理发送时间间隔和发送任务控制
  - 执行 ID 递增等特殊功能
  - 管理发送次数和计数

### 3. 设备交互层

- **`CANController`类**：管理与 CAN 设备的通信

  - 处理设备打开/关闭和通道管理
  - 提供底层消息发送接口`transmit_msg`
  - 跟踪发送计数和状态

- **`MessageHandler`类**：处理 CAN 消息的创建和格式转换
  - 提供`create_can_msg`和`create_canfd_msg`等方法创建标准格式的 CAN 消息
  - 处理标准帧/扩展帧、数据帧/远程帧等不同类型的报文

### 4. 硬件驱动层

- **`zlgcan.py`**：ZLG CAN 设备的 Python API 封装
  - 提供原始设备访问和消息发送 API
  - 定义硬件相关的结构体和常量

## 数据流程

1. **参数准备阶段**：

   - 用户在`MessagePanel`界面上配置报文参数(ID、数据、帧类型等)
   - 设置发送参数(发送次数、每次帧数、发送间隔)
   - 选择发送方式(正常发送/自发自收)和 ID 递增选项

2. **发送初始化阶段**：

   - 点击"发送"按钮触发`_on_send_clicked`方法
   - 创建包含所有必要参数的`msg_info`字典
   - 调用`MessageSender.start_sending`启动发送任务

3. **发送执行阶段**：

   - `MessageSendThread`线程根据发送参数控制发送节奏
   - 通过回调函数`_send_message`将消息传递给主窗口
   - 主窗口的`_on_send_message`方法处理最终的消息发送
   - `CANController.transmit_msg`将消息发送到实际硬件

4. **消息生成阶段**：

   - 根据是否为 CANFD、是否启用 BRS 等参数选择合适的消息创建方法
   - 使用`MessageHandler`创建标准格式的 CAN/CANFD 消息
   - 设置正确的帧标志(扩展帧、远程帧等)和发送模式

5. **反馈与控制阶段**：
   - 发送完成或出错时发出相应信号
   - 界面根据信号更新状态和提示信息
   - 用户可随时停止发送任务

## 特殊功能

1. **循环发送**：通过设置`send_count`为-1 实现无限循环发送
2. **ID 递增**：每发送一帧自动增加 ID，对标准帧和扩展帧有不同处理(分别是 11 位和 29 位)
3. **自发自收**：设置`transmit_type=2`，使设备在发送后自动接收该帧
4. **多帧发送**：每次可发送多帧(`send_num`参数)，以提高发送效率
5. **发送间隔精确控制**：通过`send_period`参数控制发送间隔，单位为毫秒

## 与矩阵消息发送的区别

矩阵消息发送工作在信号层级(更高抽象层次)，而自定义发送直接操作 CAN 帧(更底层)。自定义发送更适合测试特定帧、调试和底层协议分析，给用户提供对每个字节的完全控制权。

## 报文参数说明

### 基本参数

- **ID**：CAN 报文标识符，标准帧为 11 位，扩展帧为 29 位
- **数据内容**：以空格分隔的十六进制字节值
- **数据长度**：报文数据长度，标准 CAN 为 0-8，CANFD 可达 64 字节

### 帧类型参数

- **帧类型**：标准帧(11 位 ID)或扩展帧(29 位 ID)
- **帧格式**：数据帧或远程帧
- **CAN 类型**：标准 CAN 或 CANFD（带 BRS 或不带 BRS）

### 发送控制参数

- **发送次数**：-1 表示循环发送，正整数表示发送指定次数
- **每次帧数**：每次发送的帧数量
- **发送间隔**：两次发送之间的时间间隔(毫秒)
- **ID 递增**：每发送一帧后自动增加 ID 值
- **发送方式**：正常发送或自发自收模式

## 实现细节

### 高精度定时控制

- 使用高精度时间戳控制发送间隔
- 考虑处理时间补偿，确保周期稳定

### 线程安全设计

- 使用线程锁保护共享资源
- 信号槽机制确保 GUI 更新安全

### 错误处理与恢复

- 发送错误自动停止并通知用户
- 发送完成自动恢复 UI 状态

### 优化与性能

- 批量发送提高效率
- 合理使用线程优先级，确保实时性
