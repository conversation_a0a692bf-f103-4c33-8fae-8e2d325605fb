# 进制转换功能说明

## 功能概述

进制转换功能是 ZLGCAN 仿真软件的实用工具之一，提供了二进制、十进制、十六进制和 ASCII 码之间的相互转换能力。该功能可以通过主菜单栏中的"进制转换"选项打开，用于辅助用户进行数值与字符的编码转换工作。

## 功能特点

- **多种进制支持**：支持二进制、十进制、十六进制和 ASCII 码之间的相互转换
- **双模式设计**：提供单字符模式和字符串模式，满足不同场景需求
- **实时转换**：在任一输入框中输入数据，其他输入框将实时显示转换结果
- **输入验证**：每种进制的输入框都有相应的输入验证，确保输入数据的正确性
- **用户友好**：简洁直观的界面设计，提供清晰的操作提示

## 使用方法

1. 在软件主界面，点击菜单栏中的一级菜单"进制转换"选项
2. 进制转换对话框将会打开，默认为单字符模式
3. 在任一输入框中输入数据，其他输入框将自动显示转换结果
4. 可以通过顶部的单选按钮在单字符模式和字符串模式之间切换
5. 使用完毕后，点击"关闭"按钮关闭对话框

## 模式说明

### 单字符模式

- **适用场景**：单个字符与其 ASCII 码值之间的转换
- **工作原理**：直接将单个字符与其对应的数值表示进行转换
- **输入限制**：ASCII 输入框仅处理最后输入的一个字符
- **显示规则**：仅当数值在可打印 ASCII 范围(32-126)内时，才会显示对应字符

### 字符串模式

- **适用场景**：将多个字符组成的字符串转换为十六进制表示
- **工作原理**：将字符串中的每个字符转换为对应的两位十六进制数值，然后组合显示
- **输入/输出**：可以在 ASCII 输入框中输入任意长度的字符串，十六进制输入框将显示对应的十六进制表示
- **特殊说明**：由于长字符串的二进制和十进制表示通常不具有实际意义，这两个输入框在字符串模式下不显示值

## 输入框说明

1. **二进制输入框**：

   - 只接受 0 和 1 作为有效输入
   - 内部使用`int(text, 2)`进行转换

2. **十进制输入框**：

   - 只接受数字 0-9 作为有效输入
   - 内部使用`int(text)`进行转换

3. **十六进制输入框**：

   - 接受 0-9 和 A-F(不区分大小写)作为有效输入
   - 内部使用`int(text, 16)`进行转换
   - 在字符串模式下显示字符串的十六进制表示

4. **ASCII 输入框**：
   - 单字符模式下仅处理最后一个字符
   - 字符串模式下处理整个输入字符串
   - 使用`ord()`和`chr()`函数进行字符和数值之间的转换

## 应用场景

- **协议开发**：在进行通信协议开发时，快速进行各种数值编码的转换
- **调试工具**：在调试过程中，快速查看 ASCII 字符的十六进制表示
- **数据分析**：分析二进制数据时，快速转换为更易读的十六进制或十进制
- **编码转换**：将文本字符串转换为十六进制表示，用于数据传输或存储

## 注意事项

1. 在单字符模式下，如果输入的数值超出可打印 ASCII 范围(32-126)，ASCII 输入框将不显示字符
2. 切换模式时，所有输入框的内容将被清空
3. 使用"清空"按钮可以一次性清空所有输入框
4. 在字符串模式下，十六进制表示中每个字符占用两位十六进制数
