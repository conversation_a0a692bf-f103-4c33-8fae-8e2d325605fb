# CAN HEX 计算器功能设计

## 1. 功能概述

CAN HEX 计算器是 PyZLG_CAN 项目中的一个重要工具，专门用于 CAN 报文信号的编解码计算。该工具能够根据信号的物理值生成对应的原始二进制数据并填充到指定的 CAN 报文位置，也能从原始 CAN 报文数据中解析出信号的物理值。主要用于辅助开发人员进行 CAN 报文数据的手动构造和验证。

## 2. 设计原理

### 2.1 信号编码原理

在 CAN 协议中，信号数据的编码需要遵循以下计算公式：

- **原始值（Raw Value）= （物理值 - 偏移量）/ 精度**
- **物理值（Physical Value）= 原始值 × 精度 + 偏移量**

其中：

- **原始值**：存储在 CAN 报文中的实际二进制数据所表示的整数值
- **物理值**：具有实际物理意义的工程量值
- **精度**：工程量的最小分辨率
- **偏移量**：工程量的偏移值

编码过程基本步骤：

1. 根据物理值计算原始值
2. 将原始值转换为二进制
3. 根据字节序（LSB/MSB）将二进制位放置到 CAN 报文的指定位置

### 2.2 字节序（Byte Order）

CAN 报文中有两种主要的字节序编码方式：

1. **Intel 格式（LSB 格式）**：

   - 信号从起始位开始向高位方向增长
   - 从低字节向高字节填充
   - 适用于多数情况

2. **Motorola 格式（MSB 格式）**：
   - 信号从起始位开始向低位方向增长
   - 从高字节向低字节填充
   - 在某些特定场景下使用

## 3. 功能实现

### 3.1 核心功能

CAN HEX 计算器提供以下核心功能：

1. **信号物理值与原始值转换**

   - 物理值转原始值：根据精度和偏移量计算
   - 原始值转物理值：根据精度和偏移量反算

2. **二进制数据可视化**

   - 直观显示每个比特的位置和值
   - 高亮显示当前编辑的信号在 CAN 报文中的位置

3. **报文生成与复制**

   - 根据配置的信号生成完整的 CAN 报文十六进制字符串
   - 支持一键复制生成的报文用于其他工具

4. **多字节长度支持**

   - 支持 8 字节标准 CAN 报文
   - 支持 16 字节和 64 字节扩展报文

5. **测试用例**
   - 内置多种典型测试用例，便于快速验证

### 3.2 技术实现

CAN HEX 计算器基于 PyQt5 实现，主要包含以下几个关键组件：

1. **信号属性配置面板**

   - 起始位（StartBit）：信号在 CAN 报文中的起始位
   - 位长度（BitLength）：信号占用的位数
   - 精度（Resolution）：物理值与原始值的比例因子
   - 偏移量（Offset）：物理值的偏移量
   - 字节格式选择：Motorola LSB 或 Motorola MSB

2. **报文位图表格**

   - 8×N 网格表示 CAN 报文的位布局（N 为字节数）
   - 每一行代表一个字节，每一列代表字节中的一个位
   - 实时可视化展示报文中每一位的值

3. **数据转换逻辑**

   - `CANMessage_lsb()`：LSB 格式下的报文生成算法
   - `CANMessage_msb()`：MSB 格式下的报文生成算法
   - 支持跨字节信号的填充与提取

4. **实时双向更新**
   - 物理值变更时自动更新原始值
   - 原始值变更时自动更新物理值
   - 任一参数变更时自动更新可视化表格

## 4. 使用方法

### 4.1 基本操作流程

1. **配置信号参数**

   - 设置起始位、位长度、精度和偏移量
   - 选择字节格式（LSB 或 MSB）
   - 选择报文长度（8/16/64 字节）

2. **输入信号值**

   - 可以输入物理值，系统会自动计算原始值
   - 也可以输入原始值（十六进制），系统会自动计算物理值

3. **生成报文**

   - 点击"生成报文"按钮，系统会根据配置的参数生成 CAN 报文
   - 报文会以十六进制字符串的形式显示在底部文本框中

4. **复制报文**
   - 点击"复制报文"按钮，将生成的报文复制到剪贴板
   - 可以粘贴到其他应用中使用

### 4.2 预置测试用例

计算器内置了四种典型测试用例：

1. **测试用例 1**：LSB 单字节测试

   - 起始位：42，位长度：2，精度：1，偏移量：0
   - 物理值：2，格式：LSB

2. **测试用例 2**：MSB 单字节测试

   - 起始位：42，位长度：2，精度：1，偏移量：0
   - 物理值：2，格式：MSB

3. **测试用例 3**：LSB 跨字节测试

   - 起始位：30，位长度：12，精度：1，偏移量：0
   - 物理值：1930，格式：LSB

4. **测试用例 4**：MSB 跨字节测试
   - 起始位：30，位长度：12，精度：1，偏移量：0
   - 物理值：1930，格式：MSB

这些测试用例可以帮助用户快速了解不同编码方式的差异和计算结果。

## 5. 异常处理

计算器实现了完善的异常处理机制：

1. **输入验证**

   - 检查所有必要参数是否已填写
   - 验证数值格式是否正确

2. **范围检查**

   - 验证原始值是否在信号位长度允许的范围内
   - 如超出范围，提示用户并阻止继续计算

3. **日志记录**
   - 记录详细的计算过程和异常信息
   - 便于问题排查

## 6. 实现特点

### 6.1 可视化设计

- **位表格可视化**：直观展示信号在 CAN 报文中的位置和值
- **起始位高亮**：用不同颜色标记信号的起始位，便于识别
- **自适应布局**：界面布局会根据窗口大小自动调整

### 6.2 性能优化

- **实时计算**：信号参数变更时实时计算并更新显示
- **批量处理**：填充位时采用批量处理，提高效率
- **信号阻断**：在批量更新时临时阻断信号，避免重复触发

### 6.3 扩展性设计

- **动态字节数**：支持动态调整报文字节数
- **模块化结构**：逻辑功能模块化，便于维护和扩展
- **独立启动**：可以作为独立工具启动，也可以从主程序中调用

## 7. 使用场景

CAN HEX 计算器在以下场景中特别有用：

1. **报文验证**：验证手动构造的 CAN 报文是否符合预期
2. **协议调试**：在测试和调试 CAN 通信协议时快速构造测试数据
3. **信号分析**：分析从 CAN 总线上捕获的原始数据
4. **开发辅助**：辅助开发人员理解和实现 CAN 信号的编解码算法

## 8. 总结

CAN HEX 计算器是 PyZLG_CAN 项目中一个强大而实用的工具，通过直观的界面和高效的算法，帮助开发人员处理 CAN 报文信号的编解码问题。它支持多种字节格式和报文长度，既可以作为独立工具使用，也可以与项目的其他功能无缝集成，为 CAN 总线通信开发和调试提供了有力支持。
