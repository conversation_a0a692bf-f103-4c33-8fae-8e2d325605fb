# CAN 设备管理设计详解

本文档详细介绍了项目中关于 CAN 设备选择、设备打开/关闭以及 CAN 通道配置/管理的设计方案和实现细节。

## 1. 设备管理架构

### 1.1 整体架构

项目采用了分层架构设计 CAN 设备管理功能：

- **UI 层**：`ChannelPanel`类提供设备和通道配置的界面交互
- **控制层**：`CANController`类负责设备和通道的核心控制逻辑
- **管理层**：`ChannelManager`类管理通道状态信息
- **驱动层**：`ZCAN`类（封装于`zlgcan.py`）提供设备驱动接口

各层之间通过信号槽机制和回调函数进行通信，保持了良好的解耦性。

### 1.2 关键组件

- **设备信息配置**：`dev_info.json`存储支持的设备类型和参数配置
- **通道配置管理**：`ConfigManager`类负责保存和加载通道配置
- **消息收发处理**：`MessageReceiveThread`类处理 CAN 消息的接收

## 2. 设备选择机制

### 2.1 设备信息管理

项目使用 JSON 格式的配置文件`api/dev_info.json`管理所有支持的设备信息：

```json
{
    "设备型号名称": {
        "dev_type": 设备类型ID,
        "chn_num": 通道数量,
        "chn_info": {
            "is_canfd": 是否支持CANFD,
            "sf_res": 是否支持终端电阻,
            "baudrate": {
                "波特率名称": 波特率设置值
            },
            "data_baudrate": {
                "数据段波特率名称": 波特率设置值
            }
        }
    }
}
```

这种设计使得系统易于扩展支持新的设备类型，只需在配置文件中添加相应设备信息。

### 2.2 设备类型常量

系统按功能将设备分为三类，在`constants.py`中定义：

```python
# 设备类型常量
USBCANFD_TYPE = (41, 42, 43)  # USBCANFD系列设备类型
USBCAN_XE_U_TYPE = (20, 21, 31)  # USBCAN-E-U系列设备类型
USBCAN_I_II_TYPE = (3, 4)  # USBCAN I/II代设备类型
```

不同设备类型在初始化和配置时有不同的处理逻辑。

### 2.3 设备选择 UI

系统在 UI 层自动从`dev_info.json`读取设备列表并展示给用户选择。设备选择后，系统会自动加载该设备支持的通道数量、波特率范围等参数供用户配置。

## 3. 设备打开/关闭流程

### 3.1 设备打开流程

1. **UI 触发**：用户在界面选择设备类型并点击"打开"按钮
2. **参数获取**：`MainWindow`从`ChannelPanel`获取设备类型和参数
3. **设备打开**：调用`CANController.open_device()`方法打开设备
4. **设备配置**：针对不同设备类型（尤其是 CANFD 设备）设置特定参数
5. **状态更新**：更新界面状态，通过信号通知其他组件设备已打开

设备打开的关键代码：

```python
def open_device(self, dev_type: int, dev_idx: int) -> bool:
    # 加载设备信息
    self._load_device_info()

    # 打开设备
    self._dev_handle = self._zcan.OpenDevice(dev_type, dev_idx, 0)
    if self._dev_handle == INVALID_DEVICE_HANDLE:
        return False

    # 设置设备信息
    for dev_name, dev_info in self._dev_info.items():
        if dev_info["dev_type"] == dev_type:
            self._cur_dev_info = dev_info.copy()
            self._cur_dev_info["name"] = dev_name
            self._cur_dev_info["index"] = dev_idx
            break

    # 配置设备特性
    self._is_canfd = dev_type in USBCANFD_TYPE
    self._res_support = dev_type in USBCANFD_TYPE or dev_type in USBCAN_XE_U_TYPE

    # 针对CANFD设备设置特别配置
    if self._is_canfd:
        # 设置CANFD时钟、标准和终端电阻
        # ...

    self._is_open = True
    return True
```

### 3.2 设备关闭流程

1. **关闭所有通道**：遍历关闭已打开的所有通道
2. **关闭设备**：调用底层 API 关闭设备
3. **清理资源**：停止所有线程，重置状态变量
4. **界面更新**：更新界面状态，通知其他组件设备已关闭

关闭设备的核心代码：

```python
def close_device(self):
    if not self._is_open:
        return

    # 停止所有通道
    for chn_idx in list(self._can_handles.keys()):
        self.close_channel(chn_idx)

    # 关闭设备
    self._zcan.CloseDevice(self._dev_handle)
    self._is_open = False

    # 重置设备信息
    self._cur_dev_info = None
    self._is_canfd = False
    self._res_support = False
```

## 4. 通道配置与管理

### 4.1 通道初始化

通道初始化过程会根据设备类型和通道配置创建相应的初始化结构：

```python
def init_channel(self, chn_idx: int, chn_cfg: ZCAN_CHANNEL_INIT_CONFIG) -> bool:
    # 根据通道类型处理
    can_type = chn_cfg.can_type
    can_type_str = "CAN" if can_type == ZCAN_TYPE_CAN else "CANFD"

    # 获取终端电阻状态
    terminal_resistance = self._resistance_enabled.get('pending_' + str(chn_idx), True)

    # 获取模式和波特率信息
    if can_type == ZCAN_TYPE_CANFD:
        # 处理CANFD通道
        # ...
    else:
        # 处理普通CAN通道
        # ...

    # 初始化通道
    self._can_handles[chn_idx] = self._zcan.InitCAN(
        self._dev_handle, chn_idx, chn_cfg)

    # 启动通道
    start_result = self._zcan.StartCAN(self._can_handles[chn_idx])

    # 设置通道状态
    self._chn_states[chn_idx] = True
    self._tx_cnts[chn_idx] = 0
    self._rx_cnts[chn_idx] = 0

    return True
```

### 4.2 通道参数配置

通道配置主要包括：

- **通道类型**：普通 CAN 或 CANFD
- **工作模式**：正常模式或只听模式
- **波特率设置**：
  - 普通 CAN：单一波特率
  - CANFD：仲裁域和数据域两种波特率
- **终端电阻**：对于支持终端电阻的设备，可启用或禁用
- **滤波器设置**：可配置接收滤波器

系统会根据设备类型自动调整可用的配置选项。

### 4.3 通道关闭

通道关闭过程包括：

1. 停止接收线程
2. 调用 API 重置通道
3. 更新通道状态
4. 通知 UI 通道已关闭

```python
def close_channel(self, chn_idx: int):
    if not self._chn_states.get(chn_idx, False):
        return

    # 停止接收线程
    if chn_idx in self._read_threads:
        self._read_threads[chn_idx].stop()
        del self._read_threads[chn_idx]

    # 重置通道
    self._zcan.ResetCAN(self._can_handles[chn_idx])
    self._chn_states[chn_idx] = False
```

### 4.4 通道状态管理

`ChannelManager`类负责管理所有通道的状态信息：

```python
def update_channel_state(self, dev_idx: int, chn_idx: int, is_open: bool, protocol_type: Optional[int] = None):
    # 确保设备索引为0
    dev_idx = 0
    channel_key = (dev_idx, chn_idx)

    if is_open:
        if protocol_type is not None:
            self._opened_channels[channel_key] = protocol_type
    else:
        if channel_key in self._opened_channels:
            del self._opened_channels[channel_key]

    # 发送通道状态改变信号
    self.channel_state_changed.emit(dev_idx, chn_idx, is_open)
```

## 5. 波特率管理

### 5.1 波特率映射

系统从`dev_info.json`文件加载波特率映射表，将人类可读的波特率名称（如"500K"）映射到底层 API 需要的值：

```python
def _load_device_info(self):
    # 加载设备信息
    with open(DEVICE_INFO_FILE, "r") as fd:
        self._dev_info = json.load(fd)

    # 构建波特率映射表
    self._baudrate_map = {}
    self._data_baudrate_map = {}

    # 遍历所有设备类型处理波特率
    for dev_name, dev_info in self._dev_info.items():
        if "chn_info" in dev_info and "is_canfd" in dev_info["chn_info"]:
            # 处理仲裁段波特率
            if "baudrate" in dev_info["chn_info"]:
                # ...处理波特率映射

            # 处理数据段波特率（仅CANFD设备）
            if dev_info["chn_info"]["is_canfd"] and "data_baudrate" in dev_info["chn_info"]:
                # ...处理数据段波特率映射
```

### 5.2 波特率转换

系统提供了转换方法，可在底层值和可读名称之间转换：

```python
def _get_readable_baudrate(self, baudrate, is_data_baudrate=False):
    # 选择合适的映射表
    baudrate_map = self._data_baudrate_map if is_data_baudrate else self._baudrate_map

    # 尝试多种格式匹配
    if baudrate in baudrate_map:
        return f"{baudrate_map[baudrate]}"

    # 尝试转换为整数查找
    try:
        int_baudrate = int(baudrate)
        if int_baudrate in baudrate_map:
            return f"{baudrate_map[int_baudrate]}"
    except (ValueError, TypeError):
        pass

    # 尝试转换为字符串查找
    str_baudrate = str(baudrate)
    if str_baudrate in baudrate_map:
        return f"{baudrate_map[str_baudrate]}"

    # 找不到时返回原值
    return f"{baudrate} (未知)"
```

## 6. 设备特性处理

### 6.1 CANFD 特性

对于 CANFD 设备，系统提供了额外的配置选项：

- **数据段波特率**：可单独设置数据段速率
- **BRS**：位速率切换功能
- **ESI**：错误状态指示器

CANFD 设备初始化代码：

```python
# 设置特定于CANFD的参数
if self._is_canfd:
    ip = self._zcan.GetIProperty(self._dev_handle)
    if ip:
        for chn in range(channel_num):
            # 设置CANFD时钟
            ret = self._zcan.SetValue(ip, f"{chn}/clock", "60000000")
            # 设置CANFD标准
            ret = self._zcan.SetValue(ip, f"{chn}/canfd_standard", "0")
            # 设置终端电阻
            if self._res_support:
                ret = self._zcan.SetValue(ip, f"{chn}/initenal_resistance", "1")
        # 释放属性接口
        self._zcan.ReleaseIProperty(ip)
```

### 6.2 终端电阻控制

对于支持终端电阻的设备，系统提供了控制功能：

```python
def set_resistance(self, chn_idx: int, enabled: bool) -> bool:
    if not self._is_open or not self._chn_states.get(chn_idx, False):
        return False

    if not self._res_support:
        return False

    # 获取设备的IProperty接口
    iproperty = self._zcan.GetIProperty(self._dev_handle)
    if not iproperty:
        return False

    # 设置终端电阻
    value = "1" if enabled else "0"
    path = f"{chn_idx}/initenal_resistance"
    result = self._zcan.SetValue(iproperty, path, value)

    # 释放IProperty接口
    self._zcan.ReleaseIProperty(iproperty)

    # 更新终端电阻状态
    if result == ZCAN_STATUS_OK:
        self._resistance_enabled[chn_idx] = enabled
        return True

    return False
```

## 7. 配置持久化

系统使用`ConfigManager`类保存和加载通道配置，确保用户的配置可以持久保存：

```python
def _save_channel_config(self, chn_idx: int):
    # 获取各个控件的当前值
    mode_combo = group.findChild(QComboBox, f"mode_combo_{chn_idx}")
    protocol_combo = group.findChild(QComboBox, f"protocol_combo_{chn_idx}")
    baudrate_combo = group.findChild(QComboBox, f"baudrate_combo_{chn_idx}")
    data_baudrate_combo = group.findChild(QComboBox, f"data_baudrate_combo_{chn_idx}")
    resistance_combo = group.findChild(QComboBox, f"resistance_combo_{chn_idx}")

    # 保存配置
    channel_config = {
        "mode": mode_combo.currentText(),
        "protocol": protocol_combo.currentText(),
        "baudrate": baudrate_combo.currentText(),
        "data_baudrate": data_baudrate_combo.currentText(),
        "resistance": resistance_combo.currentText()
    }

    # 使用ConfigManager保存配置
    self._config.set_value(ConfigType.CHANNEL, f"通道状态.通道{chn_idx}", channel_config)
```

加载配置时，优先使用保存的通道配置，如果没有则使用默认配置：

```python
def _load_channel_config(self, chn_idx: int) -> Dict[str, str]:
    # 从配置中加载通道状态
    channel_config = self._config.get_value(
        ConfigType.CHANNEL, f"通道状态.通道{chn_idx}", {})

    if not channel_config:
        # 使用默认配置
        channel_config = self._config.get_value(
            ConfigType.CHANNEL, "默认设置", {})

    # 应用配置到UI控件
    # ...

    return channel_config
```

## 8. 总结

本项目的 CAN 设备管理设计具有以下特点：

1. **灵活的设备配置**：通过 JSON 配置文件支持多种 CAN 设备
2. **分层架构**：UI 层、控制层、管理层和驱动层清晰分离
3. **参数自适应**：根据设备类型自动调整可用参数选项
4. **配置持久化**：保存用户配置，提供一致的使用体验
5. **扩展性良好**：易于添加新设备支持和扩展功能
6. **特性支持**：完整支持 CANFD 和终端电阻等高级特性

该设计确保了系统对不同类型 CAN 设备的良好兼容性，同时为用户提供了灵活而一致的操作体验。
