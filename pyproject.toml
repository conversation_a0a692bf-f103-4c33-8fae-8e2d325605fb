[tool.poetry]
name = "zlgcan-simulation"
version = "0.1.0"
description = "ZLGCAN 测试工具 - 基于PyQt5的CAN/CANFD总线通信与测试平台"
authors = ["xia<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
loguru = "^0.7.3"
numpy = "^2.2.0"
pandas = "^2.2.0"
matplotlib = "^3.10.0"
psutil = "^5.9.0"
colorama = "^0.4.6"
python-can = "^4.5.0"
cantools = "^40.2.0"
openpyxl = "^3.1.0"
pillow = "^11.1.0"
bitstruct = "^8.20.0"
PyQt5 = "^5.15.10"
crccheck = "^1.3.0"
diskcache = "^5.6.0"
fonttools = "^4.57.0"
packaging = "^24.2"
pyparsing = "^3.2.0"
python-dateutil = "^2.9.0"
pytz = "^2025.2"
setuptools = "^78.1.0"
six = "^1.17.0"
textparser = "^0.24.0"
typing-extensions = "^4.13.0"
tzdata = "^2025.2"
win32-setctime = "^1.2.0"
wrapt = "^1.17.0"

[tool.poetry.group.dev.dependencies]
black = "^25.1.0"
ruff = "^0.12.0"
pytest = "^8.4.0"
pytest-cov = "^5.0.0"
pre-commit = "^4.2.0"
mypy = "^1.10.0"
python-dotenv = "^1.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
exclude = ["pyproject.toml"]
line-length = 88
target-version = "py311"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
