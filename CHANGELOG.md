# 项目变更日志（Changelog）

本文件用于记录本项目的所有重要变更、修复和新增功能，便于团队成员追踪历史和版本演进。

## [1.1.1] - 2025-05-07

### 新增

- 新增 UI 样式工具模块 `src/ui/utils/ui_styles.py`，提供统一的界面样式设置功能
- 新增 `UIStyles` 工具类，实现界面样式的集中管理和复用

### 优化

- 优化 DBC 矩阵管理对话框，标题使用统一的加粗样式
- 界面风格统一化处理，提升用户体验和视觉一致性

## [1.1.0] - 2025-04-30

### 新增

- 新增矩阵文件管理模块，支持 DBC 矩阵文件的导入、清空和切换操作
- 新增 MSB 到 LSB 格式的 DBC 文件自动转换功能
- 新增 ICAN 和 ISCAN 双网络矩阵并行管理支持
- 新增矩阵文件安全性检查机制，包括文件大小限制和格式验证
- 新增矩阵配置持久化功能，支持自动加载上次使用的矩阵文件
- 新增从 DBC 文件名自动提取版本号功能

### 优化

- 优化矩阵文件导入流程，使用临时目录确保转换过程的安全性
- 优化矩阵解析器接口设计，提供更清晰的访问方式
- 实现矩阵管理器单例模式，确保全局唯一性

## [1.0.7] - 2025-04-28

### 新增

- 新增环境检查模块，支持 Python 环境、VC++运行库、ZLG USBCANFD 驱动多重检测与自动引导安装。

### 优化

- 环境检查：ZLG USBCANFD 驱动检测逻辑优化，兼容传统驱动服务节点、应用商店注册表节点和驱动文件多重检测，避免误报未安装。

## [1.0.6] - 2025-04-26

### 优化

- 移除"查看设备支持列表"功能，简化菜单和操作流程。
- 设备信息对话框移除"原始信息"和"DLL 路径"显示，界面更简洁。
- 优化 ZLGCAN 驱动 DLL 加载逻辑，自动根据系统架构（32 位/64 位）选择 zlgcan.dll，提升跨平台兼容性。

## [1.0.5] - 2025-04-22

### 新增

- 对 ZLG 官方支持提供的二次开发范例进行重构，UI 框架采用 PyQt5。
- 新增"更新日志"弹窗，用户可在程序"帮助"菜单下直接查看更新日志内容。
- 新增"工具"菜单，将"CAN 报文计算器""进制转换"归入"工具"菜单下。
- 新增"帮助"菜单，将"字体设置"、"关于"、"更新日志"归入"帮助"菜单下。
- 去除设备序号选择，仅支持单设备使用。

### 修复

- 无

---
