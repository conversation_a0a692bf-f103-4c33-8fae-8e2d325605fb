---
description: 
globs: 
alwaysApply: true
---
---
description: "项目特定规则"
globs: 
alwaysApply: true
---
# 项目规则
- 请充分利用“api”文件夹提供的核心能力，并遵守其规范，同时你可以参阅api\usage\dev_info_json_usage.md、api\usage\zlgcan_api_usage.md
- 关于[zlgcan.py](mdc:api/zlgcan.py)和 [dev_info.json](mdc:api/dev_info.json) 的权限：仅限于阅读和调用，禁止编辑；
- 由src\utils\paths.py统一管理本项目的所有文件的路径设置；
- 由src\utils\logger.py统一负责本项目的日志管理；
- 由src\utils\constants.py统一管理本项目中的常量定义。