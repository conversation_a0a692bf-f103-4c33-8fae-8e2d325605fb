---
description: 
globs: 
alwaysApply: true
---
---
description: "通用规范"
globs: 
alwaysApply: true
---
# 项目通用规范

## 响应语言
- 始终使用简体中文回复用户

## 技术栈
- Python 3.12 或 3.13
- GitHub Actions 自动构建和发布
- 使用 GitHub 作为代码托管平台

## 代码风格
- 保持代码简洁、可读
- 使用有意义的变量和函数名
- 添加适当的注释解释复杂逻辑
- 如果原来的代码没有清晰的注释，请你完善代码注释
- 遵循每种语言的官方风格指南

## 项目结构
- 保持项目结构清晰，遵循模块化原则
- 同类功能应放在同一目录下
- 使用适当的目录命名，反映其包含内容

## 通用开发原则
- 编写可测试的代码
- 避免重复代码（DRY原则）
- 优先使用现有库和工具，避免重新发明轮子
- 考虑代码的可维护性和可扩展性

