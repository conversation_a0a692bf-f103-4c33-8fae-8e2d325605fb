---
description: 
globs: *.md
alwaysApply: false
---
---
description: "文档规范和更新指南"
globs: *.md
alwaysApply: false
---
# 文档规范

## README.md 规范
- 保持文档结构清晰，使用适当的Markdown标记
- 确保README包含以下部分：
    - 项目简介
    - 安装说明
    - 使用方法
    - 贡献指南（如适用）
    - 许可证信息

## CHANGELOG.md 规范
- 在要求更新CHANGELOG.md时:
    ```powershell
    powershell Get-Date -Format "yyyy-MM-dd"
    ```
    - 必须使用此命令获取的实际系统日期，禁止使用想象的日期或未来日期
    - 日期格式统一为：YYYY-MM-DD
    - 版本按照时间倒序排列，新版本在前
    - 日期与版本号之间使用一个空格和一个连字符分隔

- 版本记录格式：
```
## [1.0.0] - YYYY-MM-DD
- 新增：xxx
- 修复：xxx
- 优化：xxx
```

## 文档更新原则
- 保持文档与代码同步更新
- 使用简洁明了的语言
- 提供足够的示例和说明
- 确保文档格式一致