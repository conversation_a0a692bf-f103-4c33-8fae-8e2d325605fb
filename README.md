# ZLGCAN 测试工具

## 项目简介

ZLGCAN 测试工具是一款基于 PyQt5 的 CAN/CANFD 总线通信与测试平台，专注于 USBCANFD-200U、USBCANFD-100U、USBCANFD-MINI 三种周立功 CAN 设备，提供直观易用的图形界面，适用于 CAN 总线开发、调试与故障排查。

## 主要特性

- 支持 USBCANFD-200U、USBCANFD-100U、USBCANFD-MINI 三种设备
- 支持标准 CAN 与 CANFD 协议，BRS（比特率切换）
- 支持标准帧、扩展帧、远程帧等多种消息类型
- 实时收发、过滤、日志记录
- 简洁的设备与通道管理

## 系统要求

- Windows 10 或更高版本
- Python 3.10 或更高版本
- 已安装 ZLG CAN 设备驱动

## 安装说明

### 1. 环境准备

```bash
python --version  # 确认 Python 3.10+
pip install -r requirements.txt
```

### 2. 安装本项目

```bash
git clone <本项目地址>
cd <项目目录>
python setup.py install
```

## 快速开始

```bash
python main.py
```

1. 选择设备与通道，设置参数
2. 点击"打开设备"按钮连接设备
3. 使用发送面板发送 CAN 消息
4. 在消息显示面板查看接收和发送的消息

## 使用方法

- 设备选择、通道配置、消息收发、日志查看等详见界面说明
- 详细操作请参考"使用说明"章节

## 依赖管理

- 所有依赖见 requirements.txt，安装时自动读取
- Windows 平台相关依赖（如 pywin32）自动判断添加

## 日志与常量管理

- 日志：统一由 src/utils/logger.py 管理，日志文件默认在 logs/
- 常量：集中在 src/utils/constants.py
- 路径：集中在 src/utils/paths.py

## 项目结构

```text
项目根目录/
├── api/           # ZLG CAN API 及相关文件（仅保留三种设备）
├── src/           # 源代码（核心、UI、工具）
├── config/        # 配置文件
├── logs/          # 日志文件
├── resource/      # 资源文件
├── main.py        # 程序入口
├── setup.py       # 安装脚本
├── requirements.txt # 依赖列表
└── README.md      # 项目说明
```

## 贡献指南

- 欢迎提交 issue 和 PR，建议先阅读代码结构与开发规范
- 贡献代码请遵循 PEP 8、类型注解、文档注释等最佳实践

## 许可证

本项目为内部使用工具，版权所有。
