<cmds>
	<cmd id="StartSend"   		param="UINT32:VirCH" 					send_str=":CANFD:SEND{VirCH}:RUN"  visible="false"  ch="ch0"  comment="��ʼ���ͱ���"/>
	<cmd id="StopSend"   		param="UINT32:VirCH" 					send_str=":CANFD:SEND{VirCH}:STOP"  visible="false"  ch="ch0"  comment="ֹͣ���ͱ���"/>
	<cmd id="AddData"   		param="STRING:Name,UINT8_POINT:Buff" 	send_str=":CANFD:DATA:ADD {Name},{b:Buff}"  visible="false"  ch="ch0" comment="�������"/>
	<cmd id="CheckData"   		param="STRING:Name,UINT32&amp;:Status" 	send_str=":CANFD:DATA:CHECk? {Name}" 	    recv_str="{Status}"   visible="false"  ch="ch0"/>
	<cmd id="DeleteData"   		param="STRING:Name" 					send_str=":CANFD:DATA:REMOve {Name}"  visible="false"  ch="ch0"  comment="�Ƴ���������"/>
	<cmd id="SetSendCycle"   	param="UINT32:VirCH,UINT32:Cycle" 		send_str=":CANFD:SEND{VirCH}:SET:CYCLE {Cycle}ms"  visible="false"  ch="ch0"  comment="���÷�������"/>
	<cmd id="SetSendTimes"   	param="UINT32:VirCH,UINT32:Times" 		send_str=":CANFD:SEND{VirCH}:SET:TIMES {Times}"  visible="false"  ch="ch0"  comment="�������ڷ��ʹ���"/>
	<cmd id="SetSendDelay"   	param="UINT32:VirCH,DOUBLE:Delay" 		send_str=":CANFD:SEND{VirCH}:SET:Delay {Delay}"  visible="false"  ch="ch0"  comment="���÷�����ʱ"/>
	<cmd id="CheckStatus"   	param="UINT32:VirCH,UINT32&amp;:Status" send_str=":CANFD:SEND{VirCH}:RUN?" 	    recv_str="{Status}"   visible="false"  ch="ch0"/>
	
	<cmd id="SendAll"   		param="" 		send_str=":CANFD:SEND:RUNALL"  visible="false"  ch="ch0"  comment="����ȫ��"/>
	<cmd id="StopAll"   		param="" 		send_str=":CANFD:SEND:STOPALL"  visible="false"  ch="ch0"  comment="ֹͣȫ������"/>
	<cmd id="BindVirCHData"   	param="UINT32:VirCH,STRING:Name" 		send_str=":CANFD:SEND{VirCH}:SET:DATA {Name}"  visible="false"  ch="ch0"  comment="����ͨ������"/>
	<cmd id="UnBindVirCHData"   param="UINT32:VirCH" 					send_str=":CANFD:SEND{VirCH}:RESET"  visible="false"  ch="ch0"  comment="���ͨ���еķ�������,�ͷ���ȫ�����"/>
	<cmd id="GetVirCHCount"   	param="UINT32&amp;:Count" send_str=":CANFD:SEND:NUMBer?" 	    recv_str="{Count}"   visible="false"  ch="ch0"	comment="��ȡ��ʹ��ͨ����Դ����,��ʱû��ʹ��"/>
	
	<cmd id="AddLuaScript"   	param="STRING:Name,UINT8_POINT:Buff" 	send_str=":LUA:SCRIpt:APPEND {Name}_Receive,{b:Buff}"  visible="false"  ch="ch0" comment="���lua�ű������ڴ����Ľű�"/>
	<cmd id="DeleteLuaScript"   param="STRING:Name" 	send_str=":LUA:SCRIpt:REMOVE {Name}_Receive"  visible="false"  ch="ch0" comment="ɾ��lua�ű������ڴ����Ľű�"/>
	
	<cmd id="BindScript"   		param="UINT32:VirCH,STRING:Name" 	send_str=":CANFD:SEND{VirCH}:LUA:BIND QEnd,{Name},test.MainFunc"  visible="false"  ch="ch0" comment="�󶨽ű���Ŀǰ��ʱû��ʹ��"/>
	<cmd id="UnBindScript"   	param="UINT32:VirCH" 					send_str=":CANFD:SEND{VirCH}:LUA:UNBIND QEnd"  visible="false"  ch="ch0" comment="���ű�"/>
	
	<cmd id="BindTriggerScript"   		param="UINT32:TriggerCH,STRING:Name" 	send_str=":CANFD{TriggerCH}:RECV:LUA:BIND {Name}_Name,{Name}_Receive,adv,TriggerLua{Name}"  visible="false"  ch="ch0" comment="�����ű��İ�"/>
	<cmd id="UnBindTriggerScript"   	param="UINT32:TriggerCH,STRING:Name" 	send_str=":CANFD{TriggerCH}:RECV:LUA:UNBIND {Name}_Name"  visible="false"  ch="ch0" comment="�����ű��Ľ��"/>
	<cmd id="TriggerADVBind"   			param="UINT32:TriggerCH,STRING:Name,UINT8_POINT:Buff" 	send_str=":CANFD{TriggerCH}:RECV:LUA:SET:FILTer:ADV {Name}_Name,{b:Buff}"  visible="false"  ch="ch0" comment="�����ĸ߼���������"/>
	  <!-- <scpi name="Connect" cmd=":CANFD:SEND#@CHNumber#:Connect #@SendChannel#" tips="�Ѿ�û����ʹ��" /> -->
      <!-- <scpi name="DisConnect" cmd=":CANFD:SEND:DISConnect" tips="�Ѿ�û����ʹ��" /> -->
      <!-- <scpi name="GetFreeCH" cmd=":CANFD:SEND:free?" tips="�Ѿ�û��ʹ��" /> -->
      <!-- <scpi name="ChildLuaBindAFTER"  cmd=":CANFD:DATA:BIND:LUA:EXP AFTER,#@Name#,#@Index#,"       tips="�߼��������󶨣�Ŀǰ�Ѿ�û����ʹ��"/> -->
      <!-- <scpi name="ChildLuaBindBEFORE" cmd=":CANFD:DATA:BIND:LUA:EXP BEFORE,#@Name#,#@Index#,"       tips="�߼��������󶨣�Ŀǰ�Ѿ�û����ʹ��"/> -->
      <!-- <scpi name="SendClear" 			cmd=":CANFD:SEND:CLEAR"       				tips="��������ڵײ㷢�Ͷ����е�����"/> -->
</cmds>
   
