<?xml version="1.0"?>
<info locale="device_locale_strings.xml">
	<device canfd="1">
		<value>0</value>
		<meta>
			<visible>false</visible>
			<type>options.int32</type>
			<desc>设备索引</desc>
			<options>
				<option type="int32" value="0" desc="0"></option>
				<option type="int32" value="1" desc="1"></option>
				<option type="int32" value="2" desc="2"></option>
				<option type="int32" value="3" desc="3"></option>
				<option type="int32" value="4" desc="4"></option>
				<option type="int32" value="5" desc="5"></option>
				<option type="int32" value="6" desc="6"></option>
				<option type="int32" value="7" desc="7"></option>
				<option type="int32" value="8" desc="8"></option>
				<option type="int32" value="9" desc="9"></option>
				<option type="int32" value="10" desc="10"></option>
				<option type="int32" value="11" desc="11"></option>
				<option type="int32" value="12" desc="12"></option>
				<option type="int32" value="13" desc="13"></option>
				<option type="int32" value="14" desc="14"></option>
				<option type="int32" value="15" desc="15"></option>
				<option type="int32" value="16" desc="16"></option>
				<option type="int32" value="17" desc="17"></option>
				<option type="int32" value="18" desc="18"></option>
				<option type="int32" value="19" desc="19"></option>
				<option type="int32" value="20" desc="20"></option>
				<option type="int32" value="21" desc="21"></option>
				<option type="int32" value="22" desc="22"></option>
				<option type="int32" value="23" desc="23"></option>
				<option type="int32" value="24" desc="24"></option>
				<option type="int32" value="25" desc="25"></option>
				<option type="int32" value="26" desc="26"></option>
				<option type="int32" value="27" desc="27"></option>
				<option type="int32" value="28" desc="28"></option>
				<option type="int32" value="29" desc="29"></option>
				<option type="int32" value="30" desc="30"></option>
				<option type="int32" value="31" desc="31"></option>
			</options>
		</meta>
	</device>
	<channel>
		<value>0</value>
		<meta>
			<visible>false</visible>
			<type>options.int32</type>
			<desc>通道号</desc>
			<options>
				<option type="int32" value="0" desc="Channel 0"></option>
				<option type="int32" value="1" desc="Channel 1"></option>
				<option type="int32" value="2" desc="Channel 2"></option>
				<option type="int32" value="3" desc="Channel 3"></option>
				<option type="int32" value="4" desc="Channel 4"></option>
				<option type="int32" value="5" desc="Channel 5"></option>
				<option type="int32" value="6" desc="Channel 6"></option>
				<option type="int32" value="7" desc="Channel 7"></option>
			</options>
		</meta>
		<channel_0 stream="channel_0" case="parent-value=0">
			<protocol flag="0x0052" at_initcan="pre">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>协议</desc>
					<options>
						<option type="int32" value="0" desc="CAN"></option>
						<option type="int32" value="1" desc="CAN FD"></option>
					</options>
				</meta>
			</protocol>
			<canfd_standard flag="0x0011" at_initcan="pre">
				<value>0</value>
				<meta>
					<desc>CANFD标准</desc>
					<type>options.int32</type>
					<visible>$/info/channel/channel_0/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="CAN FD ISO"></option>
						<option type="int32" value="1" desc="Non-ISO"></option>
					</options>
				</meta>
			</canfd_standard>
			<canfd_exp>
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>CANFD加速</desc>
					<visible>$/info/channel/channel_0/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="str_no"></option>
						<option type="int32" value="1" desc="str_yes"></option>
					</options>
				</meta>
			</canfd_exp>
			<canfd_abit_baud_rate flag="0x0046" at_initcan="pre">
				<value>500000</value>
				<meta>
					<type>options.int32</type>
					<desc>仲裁域波特率</desc>
					<options>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
						<option type="int32" value="50000" desc="50kbps 80%"></option>
						<option type="int32" value="0" desc="custom"></option>
					</options>
				</meta>
			</canfd_abit_baud_rate>
			<canfd_dbit_baud_rate flag="0x0047" at_initcan="pre">
				<value>2000000</value>
				<meta>
					<type>options.int32</type>
					<desc>数据域波特率</desc>
					<visible>$/info/channel/channel_0/canfd_abit_baud_rate != 7 &amp;&amp; $/info/channel/channel_0/protocol != 0&amp;&amp;$/info/channel/channel_0/canfd_exp!=0</visible>
					<options>
						<option type="int32" value="5000000" desc="5Mbps 75%"></option>
						<option type="int32" value="4000000" desc="4Mbps 80%"></option>
						<option type="int32" value="2000000" desc="2Mbps 80%"></option>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
					</options>
				</meta>
			</canfd_dbit_baud_rate>
			<baud_rate_custom flag="0x0044" at_initcan="pre">
				<value>1.0Mbps(66%),4.0Mbps(66%),(60,04C00000,01000000)</value>
				<meta>
					<visible>$/info/channel/channel_0/canfd_abit_baud_rate == 7</visible>
					<type>string</type>
					<desc>自定义波特率</desc>
				</meta>
			</baud_rate_custom>
			<work_mode initcan="work_mode">
				<value>0</value>
				<meta>
					<type>options.int32</type>
					<desc>工作模式</desc>
					<options>
						<option type="int32" value="0" desc="normal_mode"></option>
						<option type="int32" value="1" desc="readonly_mode"></option>
					</options>
				</meta>
			</work_mode>
			<initenal_resistance flag="0x000B" at_initcan="post">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</initenal_resistance>
			<auto_send flag="0x0034">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CAN</desc>
				</meta>
			</auto_send>
			<auto_send_canfd flag="0x0035">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CANFD</desc>
				</meta>
			</auto_send_canfd>
			<clear_auto_send flag="0x0036">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>清空定时发送</desc>
				</meta>
			</clear_auto_send>
			<apply_auto_send flag="0x0048">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>应用定时发送</desc>
				</meta>
			</apply_auto_send>
			<set_cn flag="0x000C">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设置序列号</desc>
				</meta>
			</set_cn>
			<get_cn flag="0x000D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取序列号</desc>
				</meta>
			</get_cn>
			<update flag="0x0005">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>升级</desc>
				</meta>
			</update>
			<update_status flag="0x0006">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>升级状态</desc>
				</meta>
			</update_status>
			<channel_err_count flag="0x000E">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>通道错误计数</desc>
				</meta>
			</channel_err_count>
			<log_size flag="0x000F">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件大小</desc>
				</meta>
			</log_size>
			<log_data flag="0x0010">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件数据</desc>
				</meta>
			</log_data>
			<device_datetime flag="0x0012">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设备时间日期</desc>
				</meta>
			</device_datetime>
			<clock flag="0x0001" at_initcan="pre">
				<value>80000000</value>
				<meta>
					<type>int32</type>
					<visible>false</visible>
					<desc>时钟</desc>
				</meta>
			</clock>
			<filter_clear flag="0x0049" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>清除滤波</desc>
				</meta>
			</filter_clear>
			<filter_mode flag="0x0031" at_initcan="post">
				<value>0</value>
				<meta>
					<type>options.int32</type>
					<desc>滤波模式</desc>
					<visible>false</visible>
					<options>
						<option type="int32" value="0" desc="filter_standard"></option>
						<option type="int32" value="1" desc="filter_extend"></option>
						<option type="int32" value="2" desc="filter_disable"></option>
					</options>
				</meta>
			</filter_mode>
			<filter_start flag="0x0032" hex="1" at_initcan="post">
				<value>ffbdd8</value>
				<meta>
					<type>uint32</type>
					<desc>滤波起始帧</desc>
					<visible>false</visible>
				</meta>
			</filter_start>
			<filter_end flag="0x0033" hex="1" at_initcan="post">
				<value>ffbe28</value>
				<meta>
					<type>uint32</type>
					<desc>滤波结束帧</desc>
					<visible>false</visible>
				</meta>
			</filter_end>
			<filter_ack flag="0x0050" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>滤波生效</desc>
				</meta>
			</filter_ack>
			<filter_batch flag="0x0045" at_initcan="post">
				<value></value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</filter_batch>
			<set_bus_usage_enable flag="0x0024">
				<value>1</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>打开关闭总线利用率上报功能</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</set_bus_usage_enable>
			<set_bus_usage_period flag="0x0025">
				<value>300</value>
				<meta>
					<visible>$/info/channel/channel_0/set_bus_usage_enable==1</visible>
					<type>uint32</type>
					<desc>设置总线利用率上报周期,范围20-2000ms</desc>
				</meta>
			</set_bus_usage_period>
			<get_bus_usage flag="0x0026">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取总线利用率</desc>
				</meta>
			</get_bus_usage>
			<tx_timeout flag="0x0051" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</tx_timeout>
			<set_tx_timestamp flag="0x0053">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</set_tx_timestamp>
			<get_tx_timestamp flag="0x0054">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</get_tx_timestamp>
			<ctrl_mode flag="0x0055">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>控制器模式</desc>
				</meta>
			</ctrl_mode>
			<auto_send_param flag="0x0057">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送参数</desc>
				</meta>
			</auto_send_param>
			<set_send_mode flag="0x0058">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备模式</desc>
				</meta>
			</set_send_mode>
			<get_device_available_tx_count flag="0x0059">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备当前可以用的发送帧缓存数量</desc>
				</meta>
			</get_device_available_tx_count>
			<clear_delay_send_queue flag="0x001D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>队列模式下取消当前正在发送的队列, 队列中未发送的数据会被清除</desc>
				</meta>
			</clear_delay_send_queue>
			<set_tx_retry_policy flag="0x001E">
				<value>2</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>设置发送失败时重试策略, 使用单次发送还是一直发送到总线关闭</desc>
					<options>
						<option type="int32" value="1" desc="tx_retry_policy_once"></option>
						<option type="int32" value="2" desc="tx_retry_policy_till_busoff"></option>
					</options>
				</meta>
			</set_tx_retry_policy>
			<get_send_mode flag="0x001F">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备模式</desc>
				</meta>
			</get_send_mode>
			<set_device_recv_merge flag="0x0020">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备合并接收</desc>
				</meta>
			</set_device_recv_merge>
			<get_device_recv_merge flag="0x0021">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备合并接收状态</desc>
				</meta>
			</get_device_recv_merge>
			<set_device_tx_echo flag="0x0022">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>强制设备发送回显</desc>
				</meta>
			</set_device_tx_echo>
			<get_device_tx_echo flag="0x0023">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备发送回显状态</desc>
				</meta>
			</get_device_tx_echo>
			<get_upload_message_compress flag="0x0027">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取报文开启压缩特性</desc>
				</meta>
			</get_upload_message_compress>
			<set_upload_message_compress flag="0x0028">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置报文开启压缩特性</desc>
				</meta>
			</set_upload_message_compress>
			<lin_initenal_resistance flag="0x0029" at_initcan="post">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</lin_initenal_resistance>
			<get_device_uds_support flag="0x002A">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>CAN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_uds_support>
			<get_device_lin_uds_support flag="0x002B">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_lin_uds_support>
			<set_tx_mode flag="0x002C">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>发送模式</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</set_tx_mode>
			<get_tx_mode flag="0x002D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>获取发送模式</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_tx_mode>
		</channel_0>
		<channel_1 stream="channel_1" case="parent-value=1">
			<protocol flag="0x0152" at_initcan="pre">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>协议</desc>
					<options>
						<option type="int32" value="0" desc="CAN"></option>
						<option type="int32" value="1" desc="CAN FD"></option>
					</options>
				</meta>
			</protocol>
			<canfd_standard flag="0x0111" at_initcan="pre">
				<value>0</value>
				<meta>
					<desc>CANFD标准</desc>
					<type>options.int32</type>
					<visible>$/info/channel/channel_1/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="CAN FD ISO"></option>
						<option type="int32" value="1" desc="Non-ISO"></option>
					</options>
				</meta>
			</canfd_standard>
			<canfd_exp>
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>CANFD加速</desc>
					<visible>$/info/channel/channel_1/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="str_no"></option>
						<option type="int32" value="1" desc="str_yes"></option>
					</options>
				</meta>
			</canfd_exp>
			<canfd_abit_baud_rate flag="0x0146" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>仲裁域波特率</desc>
					<options>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
						<option type="int32" value="50000" desc="50kbps 80%"></option>
						<option type="int32" value="0" desc="custom"></option>
					</options>
				</meta>
			</canfd_abit_baud_rate>
			<canfd_dbit_baud_rate flag="0x0147" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>数据域波特率</desc>
					<visible>$/info/channel/channel_1/canfd_abit_baud_rate != 7 &amp;&amp; $/info/channel/channel_1/protocol != 0&amp;&amp;$/info/channel/channel_1/canfd_exp!=0</visible>
					<options>
						<option type="int32" value="5000000" desc="5Mbps 75%"></option>
						<option type="int32" value="4000000" desc="4Mbps 80%"></option>
						<option type="int32" value="2000000" desc="2Mbps 80%"></option>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
					</options>
				</meta>
			</canfd_dbit_baud_rate>
			<baud_rate_custom flag="0x0144" at_initcan="pre">
				<value>1.0Mbps(66%),4.0Mbps(66%),(60,04C00000,01000000)</value>
				<meta>
					<visible>$/info/channel/channel_1/canfd_abit_baud_rate == 7</visible>
					<type>string</type>
					<desc>自定义波特率</desc>
				</meta>
			</baud_rate_custom>
			<work_mode initcan="work_mode">
				<value>0</value>
				<meta>
					<type>options.int32</type>
					<desc>工作模式</desc>
					<options>
						<option type="int32" value="0" desc="normal_mode"></option>
						<option type="int32" value="1" desc="readonly_mode"></option>
					</options>
				</meta>
			</work_mode>
			<initenal_resistance flag="0x010B" at_initcan="post">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</initenal_resistance>
			<auto_send flag="0x0134">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CAN</desc>
				</meta>
			</auto_send>
			<auto_send_canfd flag="0x0135">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CANFD</desc>
				</meta>
			</auto_send_canfd>
			<clear_auto_send flag="0x0136">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>清空定时发送</desc>
				</meta>
			</clear_auto_send>
			<apply_auto_send flag="0x0148">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>应用定时发送</desc>
				</meta>
			</apply_auto_send>
			<set_cn flag="0x010C">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设置序列号</desc>
				</meta>
			</set_cn>
			<get_cn flag="0x010D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取序列号</desc>
				</meta>
			</get_cn>
			<update flag="0x0105">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>升级</desc>
				</meta>
			</update>
			<update_status flag="0x0106">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>升级状态</desc>
				</meta>
			</update_status>
			<channel_err_count flag="0x010E">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>通道错误计数</desc>
				</meta>
			</channel_err_count>
			<log_size flag="0x010F">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件大小</desc>
				</meta>
			</log_size>
			<log_data flag="0x0110">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件数据</desc>
				</meta>
			</log_data>
			<device_datetime flag="0x0112">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设备时间日期</desc>
				</meta>
			</device_datetime>
			<clock flag="0x0101" at_initcan="pre">
				<value>80000000</value>
				<meta>
					<type>int32</type>
					<visible>false</visible>
					<desc>时钟</desc>
				</meta>
			</clock>
			<filter_clear flag="0x0149" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>清除滤波</desc>
				</meta>
			</filter_clear>
			<filter_mode flag="0x0131" at_initcan="post">
				<value>2</value>
				<meta>
					<type>options.int32</type>
					<desc>滤波模式</desc>
					<visible>false</visible>
					<options>
						<option type="int32" value="0" desc="filter_standard"></option>
						<option type="int32" value="1" desc="filter_extend"></option>
						<option type="int32" value="2" desc="filter_disable"></option>
					</options>
				</meta>
			</filter_mode>
			<filter_start flag="0x0132" hex="1" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<desc>滤波起始帧</desc>
					<visible>false</visible>
				</meta>
			</filter_start>
			<filter_end flag="0x0133" hex="1" at_initcan="post">
				<value>0xFFFFFFFF</value>
				<meta>
					<type>uint32</type>
					<desc>滤波结束帧</desc>
					<visible>false</visible>
				</meta>
			</filter_end>
			<filter_ack flag="0x0150" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>滤波生效</desc>
				</meta>
			</filter_ack>
			<filter_batch flag="0x0145" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</filter_batch>
			<set_bus_usage_enable flag="0x0124">
				<value>0</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>打开关闭总线利用率上报功能</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</set_bus_usage_enable>
			<set_bus_usage_period flag="0x0125">
				<value>1000</value>
				<meta>
					<visible>$/info/channel/channel_0/set_bus_usage_enable==1</visible>
					<type>uint32</type>
					<desc>设置总线利用率上报周期,范围20-2000ms</desc>
				</meta>
			</set_bus_usage_period>
			<get_bus_usage flag="0x0126">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取总线利用率</desc>
				</meta>
			</get_bus_usage>
			<tx_timeout flag="0x0151" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</tx_timeout>
			<set_tx_timestamp flag="0x0153">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</set_tx_timestamp>
			<get_tx_timestamp flag="0x0154">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</get_tx_timestamp>
			<ctrl_mode flag="0x0155">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>控制器模式</desc>
				</meta>
			</ctrl_mode>
			<auto_send_param flag="0x0157">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送参数</desc>
				</meta>
			</auto_send_param>
			<set_send_mode flag="0x0158">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备模式</desc>
				</meta>
			</set_send_mode>
			<get_device_available_tx_count flag="0x0159">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备当前可以用的发送帧缓存数量</desc>
				</meta>
			</get_device_available_tx_count>
			<clear_delay_send_queue flag="0x011D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>队列模式下取消当前正在发送的队列, 队列中未发送的数据会被清除</desc>
				</meta>
			</clear_delay_send_queue>
			<set_tx_retry_policy flag="0x011E">
				<value>2</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>设置发送失败时重试策略, 使用单次发送还是一直发送到总线关闭</desc>
					<options>
						<option type="int32" value="1" desc="tx_retry_policy_once"></option>
						<option type="int32" value="2" desc="tx_retry_policy_till_busoff"></option>
					</options>
				</meta>
			</set_tx_retry_policy>
			<get_send_mode flag="0x011F">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备模式</desc>
				</meta>
			</get_send_mode>
			<set_device_recv_merge flag="0x0120">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备合并接收</desc>
				</meta>
			</set_device_recv_merge>
			<get_device_recv_merge flag="0x0121">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备合并接收状态</desc>
				</meta>
			</get_device_recv_merge>
			<set_device_tx_echo flag="0x0122">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>强制设备发送回显</desc>
				</meta>
			</set_device_tx_echo>
			<get_device_tx_echo flag="0x0123">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备发送回显状态</desc>
				</meta>
			</get_device_tx_echo>
			<get_upload_message_compress flag="0x0127">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取报文开启压缩特性</desc>
				</meta>
			</get_upload_message_compress>
			<set_upload_message_compress flag="0x0128">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置报文开启压缩特性</desc>
				</meta>
			</set_upload_message_compress>
			<lin_initenal_resistance flag="0x0129" at_initcan="post">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</lin_initenal_resistance>
			<get_device_uds_support flag="0x012A">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>CAN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_uds_support>
			<get_device_lin_uds_support flag="0x012B">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_lin_uds_support>
		</channel_1>
		<channel_2 stream="channel_2" case="parent-value=2">
			<protocol flag="0x0252" at_initcan="pre">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>协议</desc>
					<options>
						<option type="int32" value="0" desc="CAN"></option>
						<option type="int32" value="1" desc="CAN FD"></option>
					</options>
				</meta>
			</protocol>
			<canfd_standard flag="0x0211" at_initcan="pre">
				<value>0</value>
				<meta>
					<desc>CANFD标准</desc>
					<type>options.int32</type>
					<visible>$/info/channel/channel_2/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="CAN FD ISO"></option>
						<option type="int32" value="1" desc="Non-ISO"></option>
					</options>
				</meta>
			</canfd_standard>
			<canfd_exp>
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>CANFD加速</desc>
					<visible>$/info/channel/channel_2/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="str_no"></option>
						<option type="int32" value="1" desc="str_yes"></option>
					</options>
				</meta>
			</canfd_exp>
			<canfd_abit_baud_rate flag="0x0246" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>仲裁域波特率</desc>
					<options>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
						<option type="int32" value="50000" desc="50kbps 80%"></option>
						<option type="int32" value="0" desc="custom"></option>
					</options>
				</meta>
			</canfd_abit_baud_rate>
			<canfd_dbit_baud_rate flag="0x0247" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>数据域波特率</desc>
					<visible>$/info/channel/channel_2/canfd_abit_baud_rate != 7 &amp;&amp; $/info/channel/channel_2/protocol != 0&amp;&amp;$/info/channel/channel_2/canfd_exp!=0</visible>
					<options>
						<option type="int32" value="5000000" desc="5Mbps 75%"></option>
						<option type="int32" value="4000000" desc="4Mbps 80%"></option>
						<option type="int32" value="2000000" desc="2Mbps 80%"></option>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
					</options>
				</meta>
			</canfd_dbit_baud_rate>
			<baud_rate_custom flag="0x0244" at_initcan="pre">
				<value>1.0Mbps(66%),4.0Mbps(66%),(60,04C00000,01000000)</value>
				<meta>
					<visible>$/info/channel/channel_2/canfd_abit_baud_rate == 7</visible>
					<type>string</type>
					<desc>自定义波特率</desc>
				</meta>
			</baud_rate_custom>
			<work_mode initcan="work_mode">
				<value>0</value>
				<meta>
					<type>options.int32</type>
					<desc>工作模式</desc>
					<options>
						<option type="int32" value="0" desc="normal_mode"></option>
						<option type="int32" value="1" desc="readonly_mode"></option>
					</options>
				</meta>
			</work_mode>
			<initenal_resistance flag="0x020B" at_initcan="post">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</initenal_resistance>
			<auto_send flag="0x0234">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CAN</desc>
				</meta>
			</auto_send>
			<auto_send_canfd flag="0x0235">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CANFD</desc>
				</meta>
			</auto_send_canfd>
			<clear_auto_send flag="0x0236">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>清空定时发送</desc>
				</meta>
			</clear_auto_send>
			<apply_auto_send flag="0x0248">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>应用定时发送</desc>
				</meta>
			</apply_auto_send>
			<set_cn flag="0x020C">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设置序列号</desc>
				</meta>
			</set_cn>
			<get_cn flag="0x020D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取序列号</desc>
				</meta>
			</get_cn>
			<update flag="0x0205">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>升级</desc>
				</meta>
			</update>
			<update_status flag="0x0206">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>升级状态</desc>
				</meta>
			</update_status>
			<channel_err_count flag="0x020E">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>通道错误计数</desc>
				</meta>
			</channel_err_count>
			<log_size flag="0x020F">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件大小</desc>
				</meta>
			</log_size>
			<log_data flag="0x0210">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件数据</desc>
				</meta>
			</log_data>
			<device_datetime flag="0x0212">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设备时间日期</desc>
				</meta>
			</device_datetime>
			<clock flag="0x0201" at_initcan="pre">
				<value>80000000</value>
				<meta>
					<type>int32</type>
					<visible>false</visible>
					<desc>时钟</desc>
				</meta>
			</clock>
			<filter_clear flag="0x0249" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>清除滤波</desc>
				</meta>
			</filter_clear>
			<filter_mode flag="0x0231" at_initcan="post">
				<value>2</value>
				<meta>
					<type>options.int32</type>
					<desc>滤波模式</desc>
					<visible>false</visible>
					<options>
						<option type="int32" value="0" desc="filter_standard"></option>
						<option type="int32" value="1" desc="filter_extend"></option>
						<option type="int32" value="2" desc="filter_disable"></option>
					</options>
				</meta>
			</filter_mode>
			<filter_start flag="0x0232" hex="1" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<desc>滤波起始帧</desc>
					<visible>false</visible>
				</meta>
			</filter_start>
			<filter_end flag="0x0233" hex="1" at_initcan="post">
				<value>0xFFFFFFFF</value>
				<meta>
					<type>uint32</type>
					<desc>滤波结束帧</desc>
					<visible>false</visible>
				</meta>
			</filter_end>
			<filter_ack flag="0x0250" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>滤波生效</desc>
				</meta>
			</filter_ack>
			<filter_batch flag="0x0245" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</filter_batch>
			<set_bus_usage_enable flag="0x0224">
				<value>0</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>打开关闭总线利用率上报功能</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</set_bus_usage_enable>
			<set_bus_usage_period flag="0x0225">
				<value>1000</value>
				<meta>
					<visible>$/info/channel/channel_0/set_bus_usage_enable==1</visible>
					<type>uint32</type>
					<desc>设置总线利用率上报周期,范围20-2000ms</desc>
				</meta>
			</set_bus_usage_period>
			<get_bus_usage flag="0x0226">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取总线利用率</desc>
				</meta>
			</get_bus_usage>
			<tx_timeout flag="0x0251" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</tx_timeout>
			<set_tx_timestamp flag="0x0253">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</set_tx_timestamp>
			<get_tx_timestamp flag="0x0254">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</get_tx_timestamp>
			<ctrl_mode flag="0x0255">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>控制器模式</desc>
				</meta>
			</ctrl_mode>
			<auto_send_param flag="0x0257">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送参数</desc>
				</meta>
			</auto_send_param>
			<set_send_mode flag="0x0258">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备模式</desc>
				</meta>
			</set_send_mode>
			<get_device_available_tx_count flag="0x0259">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备当前可以用的发送帧缓存数量</desc>
				</meta>
			</get_device_available_tx_count>
			<clear_delay_send_queue flag="0x021D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>队列模式下取消当前正在发送的队列, 队列中未发送的数据会被清除</desc>
				</meta>
			</clear_delay_send_queue>
			<set_tx_retry_policy flag="0x021E">
				<value>2</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>设置发送失败时重试策略, 使用单次发送还是一直发送到总线关闭</desc>
					<options>
						<option type="int32" value="1" desc="tx_retry_policy_once"></option>
						<option type="int32" value="2" desc="tx_retry_policy_till_busoff"></option>
					</options>
				</meta>
			</set_tx_retry_policy>
			<get_send_mode flag="0x021F">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备模式</desc>
				</meta>
			</get_send_mode>
			<set_device_recv_merge flag="0x0220">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备合并接收</desc>
				</meta>
			</set_device_recv_merge>
			<get_device_recv_merge flag="0x0221">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备合并接收状态</desc>
				</meta>
			</get_device_recv_merge>
			<set_device_tx_echo flag="0x0222">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>强制设备发送回显</desc>
				</meta>
			</set_device_tx_echo>
			<get_device_tx_echo flag="0x0223">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备发送回显状态</desc>
				</meta>
			</get_device_tx_echo>
			<get_upload_message_compress flag="0x0227">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取报文开启压缩特性</desc>
				</meta>
			</get_upload_message_compress>
			<set_upload_message_compress flag="0x0228">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置报文开启压缩特性</desc>
				</meta>
			</set_upload_message_compress>
			<lin_initenal_resistance flag="0x0229" at_initcan="post">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</lin_initenal_resistance>
			<get_device_uds_support flag="0x022A">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>CAN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_uds_support>
			<get_device_lin_uds_support flag="0x022B">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_lin_uds_support>
		</channel_2>
		<channel_3 stream="channel_3" case="parent-value=3">
			<protocol flag="0x0352" at_initcan="pre">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>协议</desc>
					<options>
						<option type="int32" value="0" desc="CAN"></option>
						<option type="int32" value="1" desc="CAN FD"></option>
					</options>
				</meta>
			</protocol>
			<canfd_standard flag="0x0311" at_initcan="pre">
				<value>0</value>
				<meta>
					<desc>CANFD标准</desc>
					<type>options.int32</type>
					<visible>$/info/channel/channel_3/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="CAN FD ISO"></option>
						<option type="int32" value="1" desc="Non-ISO"></option>
					</options>
				</meta>
			</canfd_standard>
			<canfd_exp>
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>CANFD加速</desc>
					<visible>$/info/channel/channel_3/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="str_no"></option>
						<option type="int32" value="1" desc="str_yes"></option>
					</options>
				</meta>
			</canfd_exp>
			<canfd_abit_baud_rate flag="0x0346" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>仲裁域波特率</desc>
					<options>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
						<option type="int32" value="50000" desc="50kbps 80%"></option>
						<option type="int32" value="0" desc="custom"></option>
					</options>
				</meta>
			</canfd_abit_baud_rate>
			<canfd_dbit_baud_rate flag="0x0347" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>数据域波特率</desc>
					<visible>$/info/channel/channel_3/canfd_abit_baud_rate != 7 &amp;&amp; $/info/channel/channel_3/protocol != 0&amp;&amp;$/info/channel/channel_3/canfd_exp!=0</visible>
					<options>
						<option type="int32" value="5000000" desc="5Mbps 75%"></option>
						<option type="int32" value="4000000" desc="4Mbps 80%"></option>
						<option type="int32" value="2000000" desc="2Mbps 80%"></option>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
					</options>
				</meta>
			</canfd_dbit_baud_rate>
			<baud_rate_custom flag="0x0344" at_initcan="pre">
				<value>1.0Mbps(66%),4.0Mbps(66%),(60,04C00000,01000000)</value>
				<meta>
					<visible>$/info/channel/channel_3/canfd_abit_baud_rate == 7</visible>
					<type>string</type>
					<desc>自定义波特率</desc>
				</meta>
			</baud_rate_custom>
			<work_mode initcan="work_mode">
				<value>0</value>
				<meta>
					<type>options.int32</type>
					<desc>工作模式</desc>
					<options>
						<option type="int32" value="0" desc="normal_mode"></option>
						<option type="int32" value="1" desc="readonly_mode"></option>
					</options>
				</meta>
			</work_mode>
			<initenal_resistance flag="0x030B" at_initcan="post">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</initenal_resistance>
			<auto_send flag="0x0334">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CAN</desc>
				</meta>
			</auto_send>
			<auto_send_canfd flag="0x0335">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CANFD</desc>
				</meta>
			</auto_send_canfd>
			<clear_auto_send flag="0x0336">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>清空定时发送</desc>
				</meta>
			</clear_auto_send>
			<apply_auto_send flag="0x0348">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>应用定时发送</desc>
				</meta>
			</apply_auto_send>
			<set_cn flag="0x030C">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设置序列号</desc>
				</meta>
			</set_cn>
			<get_cn flag="0x030D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取序列号</desc>
				</meta>
			</get_cn>
			<update flag="0x0105">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>升级</desc>
				</meta>
			</update>
			<update_status flag="0x0306">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>升级状态</desc>
				</meta>
			</update_status>
			<channel_err_count flag="0x030E">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>通道错误计数</desc>
				</meta>
			</channel_err_count>
			<log_size flag="0x030F">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件大小</desc>
				</meta>
			</log_size>
			<log_data flag="0x0310">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件数据</desc>
				</meta>
			</log_data>
			<device_datetime flag="0x0312">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设备时间日期</desc>
				</meta>
			</device_datetime>
			<clock flag="0x0301" at_initcan="pre">
				<value>80000000</value>
				<meta>
					<type>int32</type>
					<visible>false</visible>
					<desc>时钟</desc>
				</meta>
			</clock>
			<filter_clear flag="0x0349" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>清除滤波</desc>
				</meta>
			</filter_clear>
			<filter_mode flag="0x0331" at_initcan="post">
				<value>2</value>
				<meta>
					<type>options.int32</type>
					<desc>滤波模式</desc>
					<visible>false</visible>
					<options>
						<option type="int32" value="0" desc="filter_standard"></option>
						<option type="int32" value="1" desc="filter_extend"></option>
						<option type="int32" value="2" desc="filter_disable"></option>
					</options>
				</meta>
			</filter_mode>
			<filter_start flag="0x0332" hex="1" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<desc>滤波起始帧</desc>
					<visible>false</visible>
				</meta>
			</filter_start>
			<filter_end flag="0x0333" hex="1" at_initcan="post">
				<value>0xFFFFFFFF</value>
				<meta>
					<type>uint32</type>
					<desc>滤波结束帧</desc>
					<visible>false</visible>
				</meta>
			</filter_end>
			<filter_ack flag="0x0350" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>滤波生效</desc>
				</meta>
			</filter_ack>
			<filter_batch flag="0x0345" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</filter_batch>
			<set_bus_usage_enable flag="0x0324">
				<value>0</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>打开关闭总线利用率上报功能</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</set_bus_usage_enable>
			<set_bus_usage_period flag="0x0325">
				<value>1000</value>
				<meta>
					<visible>$/info/channel/channel_0/set_bus_usage_enable==1</visible>
					<type>uint32</type>
					<desc>设置总线利用率上报周期,范围20-2000ms</desc>
				</meta>
			</set_bus_usage_period>
			<get_bus_usage flag="0x0326">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取总线利用率</desc>
				</meta>
			</get_bus_usage>
			<tx_timeout flag="0x0351" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</tx_timeout>
			<set_tx_timestamp flag="0x0353">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</set_tx_timestamp>
			<get_tx_timestamp flag="0x0354">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</get_tx_timestamp>
			<ctrl_mode flag="0x0355">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>控制器模式</desc>
				</meta>
			</ctrl_mode>
			<auto_send_param flag="0x0357">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送参数</desc>
				</meta>
			</auto_send_param>
			<set_send_mode flag="0x0358">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备模式</desc>
				</meta>
			</set_send_mode>
			<get_device_available_tx_count flag="0x0359">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备当前可以用的发送帧缓存数量</desc>
				</meta>
			</get_device_available_tx_count>
			<clear_delay_send_queue flag="0x031D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>队列模式下取消当前正在发送的队列, 队列中未发送的数据会被清除</desc>
				</meta>
			</clear_delay_send_queue>
			<set_tx_retry_policy flag="0x031E">
				<value>2</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>设置发送失败时重试策略, 使用单次发送还是一直发送到总线关闭</desc>
					<options>
						<option type="int32" value="1" desc="tx_retry_policy_once"></option>
						<option type="int32" value="2" desc="tx_retry_policy_till_busoff"></option>
					</options>
				</meta>
			</set_tx_retry_policy>
			<get_send_mode flag="0x031F">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备模式</desc>
				</meta>
			</get_send_mode>
			<set_device_recv_merge flag="0x0320">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备合并接收</desc>
				</meta>
			</set_device_recv_merge>
			<get_device_recv_merge flag="0x0321">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备合并接收状态</desc>
				</meta>
			</get_device_recv_merge>
			<set_device_tx_echo flag="0x0322">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>强制设备发送回显</desc>
				</meta>
			</set_device_tx_echo>
			<get_device_tx_echo flag="0x0323">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备发送回显状态</desc>
				</meta>
			</get_device_tx_echo>
			<get_upload_message_compress flag="0x0327">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取报文开启压缩特性</desc>
				</meta>
			</get_upload_message_compress>
			<set_upload_message_compress flag="0x0328">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置报文开启压缩特性</desc>
				</meta>
			</set_upload_message_compress>
			<lin_initenal_resistance flag="0x0329" at_initcan="post">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</lin_initenal_resistance>
			<get_device_uds_support flag="0x032A">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>CAN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_uds_support>
			<get_device_lin_uds_support flag="0x032B">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_lin_uds_support>
		</channel_3>
		<channel_4 stream="channel_4" case="parent-value=4">
			<protocol flag="0x0452" at_initcan="pre">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>协议</desc>
					<options>
						<option type="int32" value="0" desc="CAN"></option>
						<option type="int32" value="1" desc="CAN FD"></option>
					</options>
				</meta>
			</protocol>
			<canfd_standard flag="0x0411" at_initcan="pre">
				<value>0</value>
				<meta>
					<desc>CANFD标准</desc>
					<type>options.int32</type>
					<visible>$/info/channel/channel_4/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="CAN FD ISO"></option>
						<option type="int32" value="1" desc="Non-ISO"></option>
					</options>
				</meta>
			</canfd_standard>
			<canfd_exp>
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>CANFD加速</desc>
					<visible>$/info/channel/channel_4/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="str_no"></option>
						<option type="int32" value="1" desc="str_yes"></option>
					</options>
				</meta>
			</canfd_exp>
			<canfd_abit_baud_rate flag="0x0446" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>仲裁域波特率</desc>
					<options>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
						<option type="int32" value="50000" desc="50kbps 80%"></option>
						<option type="int32" value="0" desc="custom"></option>
					</options>
				</meta>
			</canfd_abit_baud_rate>
			<canfd_dbit_baud_rate flag="0x0447" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>数据域波特率</desc>
					<visible>$/info/channel/channel_4/canfd_abit_baud_rate != 7 &amp;&amp; $/info/channel/channel_4/protocol != 0&amp;&amp;$/info/channel/channel_4/canfd_exp!=0</visible>
					<options>
						<option type="int32" value="5000000" desc="5Mbps 75%"></option>
						<option type="int32" value="4000000" desc="4Mbps 80%"></option>
						<option type="int32" value="2000000" desc="2Mbps 80%"></option>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
					</options>
				</meta>
			</canfd_dbit_baud_rate>
			<baud_rate_custom flag="0x0444" at_initcan="pre">
				<value>1.0Mbps(66%),4.0Mbps(66%),(60,04C00000,01000000)</value>
				<meta>
					<visible>$/info/channel/channel_4/canfd_abit_baud_rate == 7</visible>
					<type>string</type>
					<desc>自定义波特率</desc>
				</meta>
			</baud_rate_custom>
			<work_mode initcan="work_mode">
				<value>0</value>
				<meta>
					<type>options.int32</type>
					<desc>工作模式</desc>
					<options>
						<option type="int32" value="0" desc="normal_mode"></option>
						<option type="int32" value="1" desc="readonly_mode"></option>
					</options>
				</meta>
			</work_mode>
			<initenal_resistance flag="0x040B" at_initcan="post">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</initenal_resistance>
			<auto_send flag="0x0434">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CAN</desc>
				</meta>
			</auto_send>
			<auto_send_canfd flag="0x0435">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CANFD</desc>
				</meta>
			</auto_send_canfd>
			<clear_auto_send flag="0x0436">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>清空定时发送</desc>
				</meta>
			</clear_auto_send>
			<apply_auto_send flag="0x0448">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>应用定时发送</desc>
				</meta>
			</apply_auto_send>
			<set_cn flag="0x040C">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设置序列号</desc>
				</meta>
			</set_cn>
			<get_cn flag="0x040D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取序列号</desc>
				</meta>
			</get_cn>
			<update flag="0x0405">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>升级</desc>
				</meta>
			</update>
			<update_status flag="0x0406">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>升级状态</desc>
				</meta>
			</update_status>
			<channel_err_count flag="0x040E">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>通道错误计数</desc>
				</meta>
			</channel_err_count>
			<log_size flag="0x040F">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件大小</desc>
				</meta>
			</log_size>
			<log_data flag="0x0410">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件数据</desc>
				</meta>
			</log_data>
			<device_datetime flag="0x0412">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设备时间日期</desc>
				</meta>
			</device_datetime>
			<clock flag="0x0401" at_initcan="pre">
				<value>80000000</value>
				<meta>
					<type>int32</type>
					<visible>false</visible>
					<desc>时钟</desc>
				</meta>
			</clock>
			<filter_clear flag="0x0449" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>清除滤波</desc>
				</meta>
			</filter_clear>
			<filter_mode flag="0x0431" at_initcan="post">
				<value>2</value>
				<meta>
					<type>options.int32</type>
					<desc>滤波模式</desc>
					<visible>false</visible>
					<options>
						<option type="int32" value="0" desc="filter_standard"></option>
						<option type="int32" value="1" desc="filter_extend"></option>
						<option type="int32" value="2" desc="filter_disable"></option>
					</options>
				</meta>
			</filter_mode>
			<filter_start flag="0x0432" hex="1" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<desc>滤波起始帧</desc>
					<visible>false</visible>
				</meta>
			</filter_start>
			<filter_end flag="0x0433" hex="1" at_initcan="post">
				<value>0xFFFFFFFF</value>
				<meta>
					<type>uint32</type>
					<desc>滤波结束帧</desc>
					<visible>false</visible>
				</meta>
			</filter_end>
			<filter_ack flag="0x0450" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>滤波生效</desc>
				</meta>
			</filter_ack>
			<filter_batch flag="0x0445" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</filter_batch>
			<set_bus_usage_enable flag="0x0424">
				<value>0</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>打开关闭总线利用率上报功能</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</set_bus_usage_enable>
			<set_bus_usage_period flag="0x0425">
				<value>1000</value>
				<meta>
					<visible>$/info/channel/channel_0/set_bus_usage_enable==1</visible>
					<type>uint32</type>
					<desc>设置总线利用率上报周期,范围20-2000ms</desc>
				</meta>
			</set_bus_usage_period>
			<get_bus_usage flag="0x0426">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取总线利用率</desc>
				</meta>
			</get_bus_usage>
			<tx_timeout flag="0x0451" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</tx_timeout>
			<set_tx_timestamp flag="0x0453">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</set_tx_timestamp>
			<get_tx_timestamp flag="0x0454">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</get_tx_timestamp>
			<ctrl_mode flag="0x0455">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>控制器模式</desc>
				</meta>
			</ctrl_mode>
			<auto_send_param flag="0x0457">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送参数</desc>
				</meta>
			</auto_send_param>
			<set_send_mode flag="0x0458">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备模式</desc>
				</meta>
			</set_send_mode>
			<get_device_available_tx_count flag="0x0459">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备当前可以用的发送帧缓存数量</desc>
				</meta>
			</get_device_available_tx_count>
			<clear_delay_send_queue flag="0x041D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>队列模式下取消当前正在发送的队列, 队列中未发送的数据会被清除</desc>
				</meta>
			</clear_delay_send_queue>
			<set_tx_retry_policy flag="0x041E">
				<value>2</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>设置发送失败时重试策略, 使用单次发送还是一直发送到总线关闭</desc>
					<options>
						<option type="int32" value="1" desc="tx_retry_policy_once"></option>
						<option type="int32" value="2" desc="tx_retry_policy_till_busoff"></option>
					</options>
				</meta>
			</set_tx_retry_policy>
			<get_send_mode flag="0x041F">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备模式</desc>
				</meta>
			</get_send_mode>
			<set_device_recv_merge flag="0x0420">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备合并接收</desc>
				</meta>
			</set_device_recv_merge>
			<get_device_recv_merge flag="0x0421">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备合并接收状态</desc>
				</meta>
			</get_device_recv_merge>
			<set_device_tx_echo flag="0x0422">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>强制设备发送回显</desc>
				</meta>
			</set_device_tx_echo>
			<get_device_tx_echo flag="0x0423">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备发送回显状态</desc>
				</meta>
			</get_device_tx_echo>
			<get_upload_message_compress flag="0x0427">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取报文开启压缩特性</desc>
				</meta>
			</get_upload_message_compress>
			<set_upload_message_compress flag="0x0428">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置报文开启压缩特性</desc>
				</meta>
			</set_upload_message_compress>
			<lin_initenal_resistance flag="0x0429" at_initcan="post">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</lin_initenal_resistance>
			<get_device_uds_support flag="0x042A">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>CAN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_uds_support>
			<get_device_lin_uds_support flag="0x042B">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_lin_uds_support>
		</channel_4>
		<channel_5 stream="channel_5" case="parent-value=5">
			<protocol flag="0x0552" at_initcan="pre">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>协议</desc>
					<options>
						<option type="int32" value="0" desc="CAN"></option>
						<option type="int32" value="1" desc="CAN FD"></option>
					</options>
				</meta>
			</protocol>
			<canfd_standard flag="0x0511" at_initcan="pre">
				<value>0</value>
				<meta>
					<desc>CANFD标准</desc>
					<type>options.int32</type>
					<visible>$/info/channel/channel_5/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="CAN FD ISO"></option>
						<option type="int32" value="1" desc="Non-ISO"></option>
					</options>
				</meta>
			</canfd_standard>
			<canfd_exp>
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>CANFD加速</desc>
					<visible>$/info/channel/channel_5/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="str_no"></option>
						<option type="int32" value="1" desc="str_yes"></option>
					</options>
				</meta>
			</canfd_exp>
			<canfd_abit_baud_rate flag="0x0546" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>仲裁域波特率</desc>
					<options>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
						<option type="int32" value="50000" desc="50kbps 80%"></option>
						<option type="int32" value="0" desc="custom"></option>
					</options>
				</meta>
			</canfd_abit_baud_rate>
			<canfd_dbit_baud_rate flag="0x0547" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>数据域波特率</desc>
					<visible>$/info/channel/channel_5/canfd_abit_baud_rate != 7 &amp;&amp; $/info/channel/channel_5/protocol != 0&amp;&amp;$/info/channel/channel_5/canfd_exp!=0</visible>
					<options>
						<option type="int32" value="5000000" desc="5Mbps 75%"></option>
						<option type="int32" value="4000000" desc="4Mbps 80%"></option>
						<option type="int32" value="2000000" desc="2Mbps 80%"></option>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
					</options>
				</meta>
			</canfd_dbit_baud_rate>
			<baud_rate_custom flag="0x0544" at_initcan="pre">
				<value>1.0Mbps(66%),4.0Mbps(66%),(60,04C00000,01000000)</value>
				<meta>
					<visible>$/info/channel/channel_5/canfd_abit_baud_rate == 7</visible>
					<type>string</type>
					<desc>自定义波特率</desc>
				</meta>
			</baud_rate_custom>
			<work_mode initcan="work_mode">
				<value>0</value>
				<meta>
					<type>options.int32</type>
					<desc>工作模式</desc>
					<options>
						<option type="int32" value="0" desc="normal_mode"></option>
						<option type="int32" value="1" desc="readonly_mode"></option>
					</options>
				</meta>
			</work_mode>
			<initenal_resistance flag="0x050B" at_initcan="post">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</initenal_resistance>
			<auto_send flag="0x0534">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CAN</desc>
				</meta>
			</auto_send>
			<auto_send_canfd flag="0x0535">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CANFD</desc>
				</meta>
			</auto_send_canfd>
			<clear_auto_send flag="0x0536">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>清空定时发送</desc>
				</meta>
			</clear_auto_send>
			<apply_auto_send flag="0x0548">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>应用定时发送</desc>
				</meta>
			</apply_auto_send>
			<set_cn flag="0x050C">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设置序列号</desc>
				</meta>
			</set_cn>
			<get_cn flag="0x050D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取序列号</desc>
				</meta>
			</get_cn>
			<update flag="0x0505">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>升级</desc>
				</meta>
			</update>
			<update_status flag="0x0506">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>升级状态</desc>
				</meta>
			</update_status>
			<channel_err_count flag="0x050E">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>通道错误计数</desc>
				</meta>
			</channel_err_count>
			<log_size flag="0x050F">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件大小</desc>
				</meta>
			</log_size>
			<log_data flag="0x0510">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件数据</desc>
				</meta>
			</log_data>
			<device_datetime flag="0x0512">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设备时间日期</desc>
				</meta>
			</device_datetime>
			<clock flag="0x0501" at_initcan="pre">
				<value>80000000</value>
				<meta>
					<type>int32</type>
					<visible>false</visible>
					<desc>时钟</desc>
				</meta>
			</clock>
			<filter_clear flag="0x0549" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>清除滤波</desc>
				</meta>
			</filter_clear>
			<filter_mode flag="0x0531" at_initcan="post">
				<value>2</value>
				<meta>
					<type>options.int32</type>
					<desc>滤波模式</desc>
					<visible>false</visible>
					<options>
						<option type="int32" value="0" desc="filter_standard"></option>
						<option type="int32" value="1" desc="filter_extend"></option>
						<option type="int32" value="2" desc="filter_disable"></option>
					</options>
				</meta>
			</filter_mode>
			<filter_start flag="0x0532" hex="1" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<desc>滤波起始帧</desc>
					<visible>false</visible>
				</meta>
			</filter_start>
			<filter_end flag="0x0533" hex="1" at_initcan="post">
				<value>0xFFFFFFFF</value>
				<meta>
					<type>uint32</type>
					<desc>滤波结束帧</desc>
					<visible>false</visible>
				</meta>
			</filter_end>
			<filter_ack flag="0x0550" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>滤波生效</desc>
				</meta>
			</filter_ack>
			<filter_batch flag="0x0545" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</filter_batch>
			<set_bus_usage_enable flag="0x0524">
				<value>0</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>打开关闭总线利用率上报功能</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</set_bus_usage_enable>
			<set_bus_usage_period flag="0x0525">
				<value>1000</value>
				<meta>
					<visible>$/info/channel/channel_0/set_bus_usage_enable==1</visible>
					<type>uint32</type>
					<desc>设置总线利用率上报周期,范围20-2000ms</desc>
				</meta>
			</set_bus_usage_period>
			<get_bus_usage flag="0x0526">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取总线利用率</desc>
				</meta>
			</get_bus_usage>
			<tx_timeout flag="0x0551" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</tx_timeout>
			<set_tx_timestamp flag="0x0553">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</set_tx_timestamp>
			<get_tx_timestamp flag="0x0554">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</get_tx_timestamp>
			<ctrl_mode flag="0x0555">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>控制器模式</desc>
				</meta>
			</ctrl_mode>
			<auto_send_param flag="0x0557">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送参数</desc>
				</meta>
			</auto_send_param>
			<set_send_mode flag="0x0558">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备模式</desc>
				</meta>
			</set_send_mode>
			<get_device_available_tx_count flag="0x0559">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备当前可以用的发送帧缓存数量</desc>
				</meta>
			</get_device_available_tx_count>
			<clear_delay_send_queue flag="0x051D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>队列模式下取消当前正在发送的队列, 队列中未发送的数据会被清除</desc>
				</meta>
			</clear_delay_send_queue>
			<set_tx_retry_policy flag="0x051E">
				<value>2</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>设置发送失败时重试策略, 使用单次发送还是一直发送到总线关闭</desc>
					<options>
						<option type="int32" value="1" desc="tx_retry_policy_once"></option>
						<option type="int32" value="2" desc="tx_retry_policy_till_busoff"></option>
					</options>
				</meta>
			</set_tx_retry_policy>
			<get_send_mode flag="0x051F">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备模式</desc>
				</meta>
			</get_send_mode>
			<set_device_recv_merge flag="0x0520">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备合并接收</desc>
				</meta>
			</set_device_recv_merge>
			<get_device_recv_merge flag="0x0521">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备合并接收状态</desc>
				</meta>
			</get_device_recv_merge>
			<set_device_tx_echo flag="0x0522">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>强制设备发送回显</desc>
				</meta>
			</set_device_tx_echo>
			<get_device_tx_echo flag="0x0523">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备发送回显状态</desc>
				</meta>
			</get_device_tx_echo>
			<get_upload_message_compress flag="0x0527">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取报文开启压缩特性</desc>
				</meta>
			</get_upload_message_compress>
			<set_upload_message_compress flag="0x0528">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置报文开启压缩特性</desc>
				</meta>
			</set_upload_message_compress>
			<lin_initenal_resistance flag="0x0529" at_initcan="post">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</lin_initenal_resistance>
			<get_device_uds_support flag="0x052A">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>CAN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_uds_support>
			<get_device_lin_uds_support flag="0x052B">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_lin_uds_support>
		</channel_5>
		<channel_6 stream="channel_6" case="parent-value=6">
			<protocol flag="0x0652" at_initcan="pre">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>协议</desc>
					<options>
						<option type="int32" value="0" desc="CAN"></option>
						<option type="int32" value="1" desc="CAN FD"></option>
					</options>
				</meta>
			</protocol>
			<canfd_standard flag="0x0611" at_initcan="pre">
				<value>0</value>
				<meta>
					<desc>CANFD标准</desc>
					<type>options.int32</type>
					<visible>$/info/channel/channel_6/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="CAN FD ISO"></option>
						<option type="int32" value="1" desc="Non-ISO"></option>
					</options>
				</meta>
			</canfd_standard>
			<canfd_exp>
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>CANFD加速</desc>
					<visible>$/info/channel/channel_6/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="str_no"></option>
						<option type="int32" value="1" desc="str_yes"></option>
					</options>
				</meta>
			</canfd_exp>
			<canfd_abit_baud_rate flag="0x0646" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>仲裁域波特率</desc>
					<options>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
						<option type="int32" value="50000" desc="50kbps 80%"></option>
						<option type="int32" value="0" desc="custom"></option>
					</options>
				</meta>
			</canfd_abit_baud_rate>
			<canfd_dbit_baud_rate flag="0x0647" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>数据域波特率</desc>
					<visible>$/info/channel/channel_6/canfd_abit_baud_rate != 7 &amp;&amp; $/info/channel/channel_6/protocol != 0&amp;&amp;$/info/channel/channel_6/canfd_exp!=0</visible>
					<options>
						<option type="int32" value="5000000" desc="5Mbps 75%"></option>
						<option type="int32" value="4000000" desc="4Mbps 80%"></option>
						<option type="int32" value="2000000" desc="2Mbps 80%"></option>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
					</options>
				</meta>
			</canfd_dbit_baud_rate>
			<baud_rate_custom flag="0x0644" at_initcan="pre">
				<value>1.0Mbps(66%),4.0Mbps(66%),(60,04C00000,01000000)</value>
				<meta>
					<visible>$/info/channel/channel_6/canfd_abit_baud_rate == 7</visible>
					<type>string</type>
					<desc>自定义波特率</desc>
				</meta>
			</baud_rate_custom>
			<work_mode initcan="work_mode">
				<value>0</value>
				<meta>
					<type>options.int32</type>
					<desc>工作模式</desc>
					<options>
						<option type="int32" value="0" desc="normal_mode"></option>
						<option type="int32" value="1" desc="readonly_mode"></option>
					</options>
				</meta>
			</work_mode>
			<initenal_resistance flag="0x060B" at_initcan="post">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</initenal_resistance>
			<auto_send flag="0x0634">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CAN</desc>
				</meta>
			</auto_send>
			<auto_send_canfd flag="0x0635">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CANFD</desc>
				</meta>
			</auto_send_canfd>
			<clear_auto_send flag="0x0636">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>清空定时发送</desc>
				</meta>
			</clear_auto_send>
			<apply_auto_send flag="0x0648">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>应用定时发送</desc>
				</meta>
			</apply_auto_send>
			<set_cn flag="0x060C">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设置序列号</desc>
				</meta>
			</set_cn>
			<get_cn flag="0x060D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取序列号</desc>
				</meta>
			</get_cn>
			<update flag="0x0605">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>升级</desc>
				</meta>
			</update>
			<update_status flag="0x0606">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>升级状态</desc>
				</meta>
			</update_status>
			<channel_err_count flag="0x060E">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>通道错误计数</desc>
				</meta>
			</channel_err_count>
			<log_size flag="0x060F">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件大小</desc>
				</meta>
			</log_size>
			<log_data flag="0x0610">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件数据</desc>
				</meta>
			</log_data>
			<device_datetime flag="0x0612">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设备时间日期</desc>
				</meta>
			</device_datetime>
			<clock flag="0x0601" at_initcan="pre">
				<value>80000000</value>
				<meta>
					<type>int32</type>
					<visible>false</visible>
					<desc>时钟</desc>
				</meta>
			</clock>
			<filter_clear flag="0x0649" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>清除滤波</desc>
				</meta>
			</filter_clear>
			<filter_mode flag="0x0631" at_initcan="post">
				<value>2</value>
				<meta>
					<type>options.int32</type>
					<desc>滤波模式</desc>
					<visible>false</visible>
					<options>
						<option type="int32" value="0" desc="filter_standard"></option>
						<option type="int32" value="1" desc="filter_extend"></option>
						<option type="int32" value="2" desc="filter_disable"></option>
					</options>
				</meta>
			</filter_mode>
			<filter_start flag="0x0632" hex="1" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<desc>滤波起始帧</desc>
					<visible>false</visible>
				</meta>
			</filter_start>
			<filter_end flag="0x0633" hex="1" at_initcan="post">
				<value>0xFFFFFFFF</value>
				<meta>
					<type>uint32</type>
					<desc>滤波结束帧</desc>
					<visible>false</visible>
				</meta>
			</filter_end>
			<filter_ack flag="0x0650" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>滤波生效</desc>
				</meta>
			</filter_ack>
			<filter_batch flag="0x0645" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</filter_batch>
			<set_bus_usage_enable flag="0x0624">
				<value>0</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>打开关闭总线利用率上报功能</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</set_bus_usage_enable>
			<set_bus_usage_period flag="0x0625">
				<value>1000</value>
				<meta>
					<visible>$/info/channel/channel_0/set_bus_usage_enable==1</visible>
					<type>uint32</type>
					<desc>设置总线利用率上报周期,范围20-2000ms</desc>
				</meta>
			</set_bus_usage_period>
			<get_bus_usage flag="0x0626">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取总线利用率</desc>
				</meta>
			</get_bus_usage>
			<tx_timeout flag="0x0651" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</tx_timeout>
			<set_tx_timestamp flag="0x0653">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</set_tx_timestamp>
			<get_tx_timestamp flag="0x0654">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</get_tx_timestamp>
			<ctrl_mode flag="0x0655">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>控制器模式</desc>
				</meta>
			</ctrl_mode>
			<auto_send_param flag="0x0657">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送参数</desc>
				</meta>
			</auto_send_param>
			<set_send_mode flag="0x0658">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备模式</desc>
				</meta>
			</set_send_mode>
			<get_device_available_tx_count flag="0x0659">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备当前可以用的发送帧缓存数量</desc>
				</meta>
			</get_device_available_tx_count>
			<clear_delay_send_queue flag="0x061D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>队列模式下取消当前正在发送的队列, 队列中未发送的数据会被清除</desc>
				</meta>
			</clear_delay_send_queue>
			<set_tx_retry_policy flag="0x061E">
				<value>2</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>设置发送失败时重试策略, 使用单次发送还是一直发送到总线关闭</desc>
					<options>
						<option type="int32" value="1" desc="tx_retry_policy_once"></option>
						<option type="int32" value="2" desc="tx_retry_policy_till_busoff"></option>
					</options>
				</meta>
			</set_tx_retry_policy>
			<get_send_mode flag="0x061F">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备模式</desc>
				</meta>
			</get_send_mode>
			<set_device_recv_merge flag="0x0620">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备合并接收</desc>
				</meta>
			</set_device_recv_merge>
			<get_device_recv_merge flag="0x0621">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备合并接收状态</desc>
				</meta>
			</get_device_recv_merge>
			<set_device_tx_echo flag="0x0622">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>强制设备发送回显</desc>
				</meta>
			</set_device_tx_echo>
			<get_device_tx_echo flag="0x0623">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备发送回显状态</desc>
				</meta>
			</get_device_tx_echo>
			<get_upload_message_compress flag="0x0627">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取报文开启压缩特性</desc>
				</meta>
			</get_upload_message_compress>
			<set_upload_message_compress flag="0x0628">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置报文开启压缩特性</desc>
				</meta>
			</set_upload_message_compress>
			<lin_initenal_resistance flag="0x0629" at_initcan="post">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</lin_initenal_resistance>
			<get_device_uds_support flag="0x062A">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>CAN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_uds_support>
			<get_device_lin_uds_support flag="0x062B">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_lin_uds_support>
		</channel_6>
		<channel_7 stream="channel_7" case="parent-value=7">
			<protocol flag="0x0752" at_initcan="pre">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>协议</desc>
					<options>
						<option type="int32" value="0" desc="CAN"></option>
						<option type="int32" value="1" desc="CAN FD"></option>
					</options>
				</meta>
			</protocol>
			<canfd_standard flag="0x0711" at_initcan="pre">
				<value>0</value>
				<meta>
					<desc>CANFD标准</desc>
					<type>options.int32</type>
					<visible>$/info/channel/channel_7/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="CAN FD ISO"></option>
						<option type="int32" value="1" desc="Non-ISO"></option>
					</options>
				</meta>
			</canfd_standard>
			<canfd_exp>
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>CANFD加速</desc>
					<visible>$/info/channel/channel_7/protocol != 0</visible>
					<options>
						<option type="int32" value="0" desc="str_no"></option>
						<option type="int32" value="1" desc="str_yes"></option>
					</options>
				</meta>
			</canfd_exp>
			<canfd_abit_baud_rate flag="0x0746" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>仲裁域波特率</desc>
					<options>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
						<option type="int32" value="50000" desc="50kbps 80%"></option>
						<option type="int32" value="0" desc="custom"></option>
					</options>
				</meta>
			</canfd_abit_baud_rate>
			<canfd_dbit_baud_rate flag="0x0747" at_initcan="pre">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>数据域波特率</desc>
					<visible>$/info/channel/channel_7/canfd_abit_baud_rate != 7 &amp;&amp; $/info/channel/channel_7/protocol != 0&amp;&amp;$/info/channel/channel_7/canfd_exp!=0</visible>
					<options>
						<option type="int32" value="5000000" desc="5Mbps 75%"></option>
						<option type="int32" value="4000000" desc="4Mbps 80%"></option>
						<option type="int32" value="2000000" desc="2Mbps 80%"></option>
						<option type="int32" value="1000000" desc="1Mbps 80%"></option>
						<option type="int32" value="800000" desc="800kbps 80%"></option>
						<option type="int32" value="500000" desc="500kbps 80%"></option>
						<option type="int32" value="250000" desc="250kbps 80%"></option>
						<option type="int32" value="125000" desc="125kbps 80%"></option>
						<option type="int32" value="100000" desc="100kbps 80%"></option>
					</options>
				</meta>
			</canfd_dbit_baud_rate>
			<baud_rate_custom flag="0x0744" at_initcan="pre">
				<value>1.0Mbps(66%),4.0Mbps(66%),(60,04C00000,01000000)</value>
				<meta>
					<visible>$/info/channel/channel_7/canfd_abit_baud_rate == 7</visible>
					<type>string</type>
					<desc>自定义波特率</desc>
				</meta>
			</baud_rate_custom>
			<work_mode initcan="work_mode">
				<value>0</value>
				<meta>
					<type>options.int32</type>
					<desc>工作模式</desc>
					<options>
						<option type="int32" value="0" desc="normal_mode"></option>
						<option type="int32" value="1" desc="readonly_mode"></option>
					</options>
				</meta>
			</work_mode>
			<initenal_resistance flag="0x070B" at_initcan="post">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</initenal_resistance>
			<auto_send flag="0x0734">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CAN</desc>
				</meta>
			</auto_send>
			<auto_send_canfd flag="0x0735">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送CANFD</desc>
				</meta>
			</auto_send_canfd>
			<clear_auto_send flag="0x0736">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>清空定时发送</desc>
				</meta>
			</clear_auto_send>
			<apply_auto_send flag="0x0748">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>应用定时发送</desc>
				</meta>
			</apply_auto_send>
			<set_cn flag="0x070C">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设置序列号</desc>
				</meta>
			</set_cn>
			<get_cn flag="0x070D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取序列号</desc>
				</meta>
			</get_cn>
			<update flag="0x0705">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>升级</desc>
				</meta>
			</update>
			<update_status flag="0x0706">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>升级状态</desc>
				</meta>
			</update_status>
			<channel_err_count flag="0x070E">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>通道错误计数</desc>
				</meta>
			</channel_err_count>
			<log_size flag="0x070F">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件大小</desc>
				</meta>
			</log_size>
			<log_data flag="0x0710">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>日志文件数据</desc>
				</meta>
			</log_data>
			<device_datetime flag="0x0712">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>设备时间日期</desc>
				</meta>
			</device_datetime>
			<clock flag="0x0701" at_initcan="pre">
				<value>80000000</value>
				<meta>
					<type>int32</type>
					<visible>false</visible>
					<desc>时钟</desc>
				</meta>
			</clock>
			<filter_clear flag="0x0749" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>清除滤波</desc>
				</meta>
			</filter_clear>
			<filter_mode flag="0x0731" at_initcan="post">
				<value>2</value>
				<meta>
					<type>options.int32</type>
					<desc>滤波模式</desc>
					<visible>false</visible>
					<options>
						<option type="int32" value="0" desc="filter_standard"></option>
						<option type="int32" value="1" desc="filter_extend"></option>
						<option type="int32" value="2" desc="filter_disable"></option>
					</options>
				</meta>
			</filter_mode>
			<filter_start flag="0x0732" hex="1" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<desc>滤波起始帧</desc>
					<visible>false</visible>
				</meta>
			</filter_start>
			<filter_end flag="0x0733" hex="1" at_initcan="post">
				<value>0xFFFFFFFF</value>
				<meta>
					<type>uint32</type>
					<desc>滤波结束帧</desc>
					<visible>false</visible>
				</meta>
			</filter_end>
			<filter_ack flag="0x0750" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc>滤波生效</desc>
				</meta>
			</filter_ack>
			<filter_batch flag="0x0745" at_initcan="post">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</filter_batch>
			<set_bus_usage_enable flag="0x0724">
				<value>0</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>打开关闭总线利用率上报功能</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</set_bus_usage_enable>
			<set_bus_usage_period flag="0x0725">
				<value>1000</value>
				<meta>
					<visible>$/info/channel/channel_0/set_bus_usage_enable==1</visible>
					<type>uint32</type>
					<desc>设置总线利用率上报周期,范围20-2000ms</desc>
				</meta>
			</set_bus_usage_period>
			<get_bus_usage flag="0x0726">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>获取总线利用率</desc>
				</meta>
			</get_bus_usage>
			<tx_timeout flag="0x0751" at_initcan="post">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</tx_timeout>
			<set_tx_timestamp flag="0x0753">
				<value>0</value>
				<meta>
					<type>uint32</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</set_tx_timestamp>
			<get_tx_timestamp flag="0x0754">
				<value>0</value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</get_tx_timestamp>
			<ctrl_mode flag="0x0755">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>控制器模式</desc>
				</meta>
			</ctrl_mode>
			<auto_send_param flag="0x0757">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>定时发送参数</desc>
				</meta>
			</auto_send_param>
			<set_send_mode flag="0x0758">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备模式</desc>
				</meta>
			</set_send_mode>
			<get_device_available_tx_count flag="0x0759">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备当前可以用的发送帧缓存数量</desc>
				</meta>
			</get_device_available_tx_count>
			<clear_delay_send_queue flag="0x071D">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>string</type>
					<desc>队列模式下取消当前正在发送的队列, 队列中未发送的数据会被清除</desc>
				</meta>
			</clear_delay_send_queue>
			<set_tx_retry_policy flag="0x071E">
				<value>2</value>
				<meta>
					<visible>true</visible>
					<type>options.int32</type>
					<desc>设置发送失败时重试策略, 使用单次发送还是一直发送到总线关闭</desc>
					<options>
						<option type="int32" value="1" desc="tx_retry_policy_once"></option>
						<option type="int32" value="2" desc="tx_retry_policy_till_busoff"></option>
					</options>
				</meta>
			</set_tx_retry_policy>
			<get_send_mode flag="0x071F">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备模式</desc>
				</meta>
			</get_send_mode>
			<set_device_recv_merge flag="0x0720">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置设备合并接收</desc>
				</meta>
			</set_device_recv_merge>
			<get_device_recv_merge flag="0x0721">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备合并接收状态</desc>
				</meta>
			</get_device_recv_merge>
			<set_device_tx_echo flag="0x0722">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>强制设备发送回显</desc>
				</meta>
			</set_device_tx_echo>
			<get_device_tx_echo flag="0x0723">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取设备发送回显状态</desc>
				</meta>
			</get_device_tx_echo>
			<get_upload_message_compress flag="0x0727">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>获取报文开启压缩特性</desc>
				</meta>
			</get_upload_message_compress>
			<set_upload_message_compress flag="0x0728">
				<value>0</value>
				<meta>
					<visible>false</visible>
					<type>uint32</type>
					<desc>设置报文开启压缩特性</desc>
				</meta>
			</set_upload_message_compress>
			<lin_initenal_resistance flag="0x0729" at_initcan="post">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN终端电阻</desc>
					<options>
						<option type="int32" value="0" desc="disable"></option>
						<option type="int32" value="1" desc="enable"></option>
					</options>
				</meta>
			</lin_initenal_resistance>
			<get_device_uds_support flag="0x072A">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>CAN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_uds_support>
			<get_device_lin_uds_support flag="0x072B">
				<value>1</value>
				<meta>
					<visible>false</visible>
					<type>options.int32</type>
					<desc>LIN UDS诊断功能支持</desc>
					<options>
						<option type="int32" value="0" desc="no"></option>
						<option type="int32" value="1" desc="yes"></option>
					</options>
				</meta>
			</get_device_lin_uds_support>
		</channel_7>
	</channel>
</info>
