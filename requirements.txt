# ZLGCAN 测试工具 依赖列表
# 说明：本文件统一管理所有Python依赖库，分为核心依赖和开发/测试依赖。
# 修改依赖时请同步更新 setup.py。
# ---------------------------------------------

##########################
# 核心依赖（运行必需）   #
##########################
altgraph==0.17.4
argparse-addons==0.12.0
bitstruct==8.20.0
cantools==40.2.1
colorama==0.4.6
crccheck==1.3.0
diskcache==5.6.3
et_xmlfile==2.0.0
fonttools==4.57.0
kiwisolver==1.4.8
loguru==0.7.3
matplotlib==3.10.1
numpy==2.2.4
openpyxl==3.1.5
packaging==24.2
pandas==2.2.3
pefile==2023.2.7
pillow==11.1.0
psutil==5.9.8
pyinstaller==6.12.0
pyinstaller-hooks-contrib==2025.2
pyparsing==3.2.3
PyQt5==5.15.11
PyQt5-Qt5==5.15.2
PyQt5_sip==12.17.0
python-can==4.5.0
python-dateutil==2.9.0.post0
pytz==2025.2
# Windows平台依赖（setup.py会自动添加）
# pywin32==310
# pywin32-ctypes==0.2.3
setuptools==78.1.0
six==1.17.0
textparser==0.24.0
typing_extensions==4.13.2
tzdata==2025.2
win32_setctime==1.2.0
wrapt==1.17.2

##########################
# 开发/测试依赖（可选） #
##########################
pytest>=7.4.0
pytest-cov>=4.1.0
python-dotenv>=1.0.0
cantools>=39.3.0
mypy>=1.5.1
# pytest==8.2.0
# pytest-cov==5.0.0
# mypy==1.10.0
# flake8==7.0.0
# black==24.4.2

# 如需开发/测试，请取消注释对应依赖
