#!/usr/bin/env python3
"""
环境测试脚本
用于验证项目环境是否正确配置
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print(f"✓ Python版本: {sys.version}")
    if sys.version_info >= (3, 11):
        print("✓ Python版本符合要求 (>=3.11)")
        return True
    else:
        print("✗ Python版本不符合要求，需要3.11或更高版本")
        return False

def test_pyqt5():
    """测试PyQt5导入"""
    try:
        from PyQt5.QtWidgets import QApplication, QWidget
        from PyQt5.QtCore import Qt, QTimer
        print("✓ PyQt5导入成功")
        return True
    except ImportError as e:
        print(f"✗ PyQt5导入失败: {e}")
        return False

def test_project_structure():
    """测试项目结构"""
    required_dirs = [
        "src",
        "src/ui", 
        "src/core",
        "src/utils",
        "api",
        "config",
        "logs"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"✗ 缺少必要目录: {', '.join(missing_dirs)}")
        return False
    else:
        print("✓ 项目目录结构完整")
        return True

def test_core_imports():
    """测试核心模块导入"""
    try:
        from src.utils.logger import get_logger
        from src.utils.paths import DEVICE_INFO_FILE
        from src.utils.app_info import APP_NAME
        from src.utils.constants import MAX_DISPLAY
        # 测试logger实例化
        logger = get_logger("test")
        print("✓ 核心工具模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 核心工具模块导入失败: {e}")
        return False

def test_api_files():
    """测试API文件"""
    api_files = [
        "api/zlgcan.py",
        "api/dev_info.json"
    ]
    
    missing_files = []
    for file_path in api_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ 缺少API文件: {', '.join(missing_files)}")
        return False
    else:
        print("✓ API文件完整")
        return True

def test_dependencies():
    """测试主要依赖"""
    dependencies = [
        ("numpy", "数值计算"),
        ("pandas", "数据处理"),
        ("matplotlib", "图表绘制"),
        ("loguru", "日志记录"),
        ("psutil", "系统监控")
    ]
    
    failed_deps = []
    for dep_name, desc in dependencies:
        try:
            __import__(dep_name)
            print(f"✓ {dep_name} ({desc}) 导入成功")
        except ImportError:
            print(f"✗ {dep_name} ({desc}) 导入失败")
            failed_deps.append(dep_name)
    
    return len(failed_deps) == 0

def main():
    """主测试函数"""
    print("=" * 50)
    print("ZLGCAN 项目环境测试")
    print("=" * 50)
    
    tests = [
        ("Python版本", test_python_version),
        ("PyQt5", test_pyqt5),
        ("项目结构", test_project_structure),
        ("核心模块", test_core_imports),
        ("API文件", test_api_files),
        ("依赖包", test_dependencies)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 环境配置完美！可以开始开发了。")
        return 0
    else:
        print("⚠️  环境配置存在问题，请检查失败的项目。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
