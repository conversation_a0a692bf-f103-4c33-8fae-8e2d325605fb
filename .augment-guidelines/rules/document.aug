# 文档规范指南
rule "readme-structure":
    description: "README.md文档结构要求"
    globs: ["README.md"]
    checks:
        - sections: ["项目简介", "安装说明", "使用方法", "贡献指南", "许可证信息"]
        - format: "markdown"
        - content: "简洁明了"
    important_notes:
        - "保持文档结构清晰，使用适当的Markdown标记"

rule "changelog-format":
    description: "CHANGELOG.md格式要求"
    globs: ["CHANGELOG.md"]
    date_format: "YYYY-MM-DD"
    version_order: "descending"
    requirements:
        - "使用系统实际日期，通过命令获取: powershell Get-Date -Format \"yyyy-MM-dd\""
        - "日期与版本号之间使用一个空格和一个连字符分隔"
        - "版本按照时间倒序排列，新版本在前"
    format: """
    ## [{version}] - {date}
    - 新增：{feature}
    - 修复：{bug}
    - 优化：{improvement}
    """

rule "doc-principles":
    description: "文档更新原则"
    globs: ["*.md"]
    principles:
        - "保持文档与代码同步更新"
        - "使用简洁明了的语言"
        - "提供足够的示例和说明"
        - "确保文档格式一致"
        - "使用系统时间戳"