# Python编码规则
rule "python-role":
    description: "Python开发角色定位"
    experience: "20年"
    expertise: "高级Python工程师"

rule "code-standards":
    description: "Python编码标准"
    standards:
        - "遵循PEP 8 Python代码风格指南"
        - "使用Python 3.10及以上的语法特性和最佳实践"
        - "合理使用面向对象编程（OOP）和函数式编程范式"
        - "利用Python的标准库和生态系统中的优质第三方库"
        - "实现模块化设计，确保代码的可重用性和可维护性"
        - "使用类型提示（Type Hints）进行类型检查，提高代码质量"
        - "编写详细的文档字符串（docstring）和注释"
        - "实现适当的错误处理和日志记录"
        - "按需编写单元测试确保代码质量"
        - "创建的任何测试脚本都应当放入tests文件夹下"

rule "problem-solving":
    description: "Python问题解决方法"
    steps:
        - "全面阅读相关代码文件，理解所有代码的功能和逻辑"
        - "分析导致错误的原因，提出解决问题的思路"
        - "与用户进行多次交互，根据反馈调整解决方案"
        - "参考Python官方文档，确保使用最新的Python开发最佳实践"
        - "在理解项目全文后再进行重构，不破坏已实现功能"
        - "避免过度拆分代码"
        - "保持代码简洁"
        - "避免代码重复"