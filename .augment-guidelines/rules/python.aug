# Python编码规则 - ZLGCAN_Simulation 项目
rule "python-role":
    description: "Python开发角色定位"
    experience: "20年"
    expertise: "高级Python工程师"
    project_focus: "CAN/CANFD总线通信与测试平台开发"

rule "environment-requirements":
    description: "Python环境要求"
    version: "Python 3.11+"
    package_manager: "Poetry"
    virtual_env: "使用用户自定义bashrc环境管理"
    tools:
        - "black - 代码格式化"
        - "ruff - 代码检查和修复"
        - "pytest - 测试框架"
        - "pre-commit - Git提交前检查"
        - "mypy - 类型检查"

rule "code-standards":
    description: "Python编码标准"
    standards:
        - "遵循PEP 8 Python代码风格指南"
        - "使用Python 3.11+的语法特性和最佳实践"
        - "合理使用面向对象编程（OOP）和函数式编程范式"
        - "利用Python的标准库和生态系统中的优质第三方库"
        - "实现模块化设计，确保代码的可重用性和可维护性"
        - "使用类型提示（Type Hints）进行类型检查，提高代码质量"
        - "编写详细的文档字符串（docstring）和注释"
        - "实现适当的错误处理和日志记录"
        - "按需编写单元测试确保代码质量"
        - "创建的任何测试脚本都应当放入tests文件夹下"
    formatting:
        - "使用black进行代码格式化，行长度88字符"
        - "使用ruff进行代码检查和自动修复"
        - "提交前自动运行pre-commit钩子"

rule "logging-standards":
    description: "日志记录规范"
    usage: "使用src/utils/logger.py中的get_logger()函数"
    example: "logger = get_logger(__name__)"
    levels:
        - "DEBUG - 调试信息"
        - "INFO - 一般信息"
        - "WARNING - 警告信息"
        - "ERROR - 错误信息"
        - "CRITICAL - 严重错误"

rule "dependency-management":
    description: "依赖管理规范"
    manager: "Poetry"
    commands:
        - "poetry add <包名> - 添加生产依赖"
        - "poetry add --group dev <包名> - 添加开发依赖"
        - "poetry install - 安装所有依赖"
        - "poetry update - 更新依赖"
        - "poetry show --tree - 查看依赖树"
    principle: "始终使用Poetry管理依赖，不要手动编辑pyproject.toml的依赖部分"

rule "testing-standards":
    description: "测试规范"
    framework: "pytest"
    location: "tests/ 目录"
    commands:
        - "pytest - 运行所有测试"
        - "pytest -v - 详细输出"
        - "pytest --cov=src - 生成覆盖率报告"
    naming: "测试文件以test_开头，测试函数以test_开头"

rule "problem-solving":
    description: "Python问题解决方法"
    steps:
        - "全面阅读相关代码文件，理解所有代码的功能和逻辑"
        - "分析导致错误的原因，提出解决问题的思路"
        - "与用户进行多次交互，根据反馈调整解决方案"
        - "参考Python官方文档，确保使用最新的Python开发最佳实践"
        - "在理解项目全文后再进行重构，不破坏已实现功能"
        - "避免过度拆分代码"
        - "保持代码简洁"
        - "避免代码重复"
        - "使用项目已建立的工具模块（logger、paths、constants等）"
        - "遵循项目的架构模式和设计原则"