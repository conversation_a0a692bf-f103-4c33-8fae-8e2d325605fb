# 开发环境管理规范 - ZLGCAN_Simulation 项目

rule "user-environment":
    description: "用户环境配置"
    bashrc_location: "C:\\Users\\<USER>\\.bashrc"
    git_for_windows: "已安装"
    custom_functions:
        - "pyinit <项目名> - 初始化新的Python项目"
        - "pyshell - 进入当前项目的虚拟环境"
        - "pyrun <命令> - 在虚拟环境中运行命令"
        - "pytest - 运行测试"
        - "pyformat - 格式化代码"
        - "check_python - 检查Python安装状态"
        - "install_python_tools - 显示Python安装指南"

rule "environment-tools":
    description: "环境工具链"
    core_tools:
        - "pipx - 全局工具管理器"
        - "poetry - 项目依赖管理"
        - "black - 代码格式化"
        - "ruff - 代码检查和修复"
        - "pytest - 测试框架"
        - "pre-commit - Git提交前自动检查"
    python_version: "3.11.9"
    package_manager: "Poetry"

rule "workflow-commands":
    description: "开发工作流命令"
    daily_workflow:
        - "pyshell - 进入开发环境"
        - "# 进行开发工作"
        - "pyformat - 格式化代码"
        - "pytest - 运行测试"
        - "exit - 退出虚拟环境"
    dependency_management:
        - "poetry add <包名> - 添加生产依赖"
        - "poetry add --group dev <包名> - 添加开发依赖"
        - "poetry install - 安装所有依赖"
        - "poetry update - 更新依赖"
        - "poetry show --tree - 查看依赖树"
    testing_commands:
        - "pytest - 运行所有测试"
        - "pytest -v - 详细输出"
        - "pytest --cov=src - 生成覆盖率报告"

rule "environment-validation":
    description: "环境验证"
    validation_steps:
        - "check_python - 检查Python环境"
        - "poetry --version - 验证Poetry安装"
        - "poetry env info - 查看虚拟环境信息"
        - "pyshell 然后 python --version - 验证虚拟环境Python版本"
    expected_results:
        - "Python 3.11+"
        - "Poetry 1.0+"
        - "虚拟环境正常激活"
        - "所有依赖正常安装"

rule "pre-commit-setup":
    description: "Pre-commit配置"
    config_file: ".pre-commit-config.yaml"
    hooks:
        - "black - 代码格式化"
        - "ruff - 代码检查"
        - "trailing-whitespace - 移除行尾空格"
        - "end-of-file-fixer - 文件末尾换行"
        - "check-yaml - YAML格式检查"
        - "check-added-large-files - 大文件检查"
        - "check-merge-conflict - 合并冲突检查"
    installation: "poetry run pre-commit install"

rule "troubleshooting":
    description: "常见问题解决"
    common_issues:
        - issue: "PyQt5安装失败"
          solution: "使用pip在Poetry环境中安装: poetry run pip install PyQt5==5.15.7"
        - issue: "虚拟环境无法激活"
          solution: "检查Poetry配置: poetry config --list, 重新创建环境: poetry env remove python && poetry install"
        - issue: "依赖冲突"
          solution: "清理锁文件: poetry lock --no-cache, 重新安装: poetry install"
        - issue: "pre-commit失败"
          solution: "手动运行格式化: pyformat, 然后重新提交"

rule "best-practices":
    description: "最佳实践"
    practices:
        - "始终在虚拟环境中进行开发"
        - "提交前运行pyformat格式化代码"
        - "定期运行pytest确保代码质量"
        - "使用Poetry管理所有依赖"
        - "遵循用户自定义的bashrc工作流"
        - "保持环境配置与项目同步"
        - "定期更新依赖和工具版本"
