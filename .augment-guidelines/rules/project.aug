# ZLGCAN_Simulation 项目特定规则

rule "project-overview":
    description: "项目概况"
    name: "ZLGCAN 测试工具"
    purpose: "基于PyQt5的CAN/CANFD总线通信与测试平台"
    devices: ["USBCANFD-200U", "USBCANFD-100U", "USBCANFD-MINI"]
    tech_stack: ["Python 3.11+", "PyQt5", "Poetry", "Windows 10+"]

rule "environment-setup":
    description: "开发环境配置"
    requirements:
        - "Python 3.11+"
        - "Poetry 项目管理"
        - "Git for Windows"
        - "用户自定义 bashrc 环境"
    commands:
        - "pyshell - 进入虚拟环境"
        - "pyrun <命令> - 在虚拟环境中运行命令"
        - "pytest - 运行测试"
        - "pyformat - 格式化代码"
        - "check_python - 检查Python环境"

rule "api-usage":
    description: "API使用规范"
    instructions: "请充分利用'api'文件夹提供的核心能力，并遵守其规范"
    references:
        - "api/usage/dev_info_json_usage.md"
        - "api/usage/zlgcan_api_usage.md"
    core_files:
        - "api/zlgcan.py - ZLG CAN API封装"
        - "api/dev_info.json - 设备信息配置"

rule "file-permissions":
    description: "文件访问权限"
    restrictions:
        - path: "api/zlgcan.py"
          access: "仅限读取和调用"
          note: "核心API文件，严禁修改"
        - path: "api/dev_info.json"
          access: "仅限读取和调用"
          note: "设备配置文件，严禁修改"
    rationale: "保护核心API的稳定性和兼容性"

rule "architecture-management":
    description: "架构管理规范"
    managers:
        - path: "src/utils/paths.py"
          role: "路径管理"
          note: "统一管理所有文件路径设置"
        - path: "src/utils/logger.py"
          role: "日志管理"
          note: "统一负责项目日志管理，使用get_logger()获取实例"
        - path: "src/utils/constants.py"
          role: "常量管理"
          note: "统一管理项目常量定义"
        - path: "src/utils/app_info.py"
          role: "应用信息"
          note: "管理应用名称、版本等信息"

rule "project-structure":
    description: "项目结构规范"
    structure:
        - "api/ - ZLG CAN API（只读）"
        - "src/ - 源代码"
        - "src/ui/ - 用户界面"
        - "src/core/ - 核心逻辑"
        - "src/utils/ - 工具模块"
        - "config/ - 配置文件"
        - "logs/ - 日志文件"
        - "tests/ - 测试文件"
        - "resource/ - 资源文件"
        - "main.py - 程序入口"