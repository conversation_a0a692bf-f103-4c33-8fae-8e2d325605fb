# 项目特定规则
rule "api-usage":
    description: "API使用规范"
    instructions: "请充分利用'api'文件夹提供的核心能力，并遵守其规范"
    references:
        - "api/usage/dev_info_json_usage.md"
        - "api/usage/zlgcan_api_usage.md"

rule "file-permissions":
    description: "文件访问权限"
    restrictions:
        - path: "api/zlgcan.py"
          access: "仅限读取"
          note: "仅限于阅读和调用，禁止编辑"
        - path: "api/dev_info.json"
          access: "仅限读取"
          note: "仅限于阅读和调用，禁止编辑"

rule "path-management":
    description: "路径管理规则"
    manager: "src/utils/paths.py"
    note: "统一管理本项目的所有文件的路径设置"

rule "logging":
    description: "日志管理"
    manager: "src/utils/logger.py"
    note: "统一负责本项目的日志管理"

rule "constants":
    description: "常量管理"
    manager: "src/utils/constants.py"
    note: "统一管理本项目中的常量定义"