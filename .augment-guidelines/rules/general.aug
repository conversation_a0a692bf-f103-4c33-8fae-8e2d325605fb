# ZLGCAN_Simulation 项目通用规范
rule "response-language":
    description: "响应语言要求"
    globs: ["*"]
    language: "zh-CN"

rule "tech-stack":
    description: "技术栈规范"
    stack:
        - "Python 3.11+"
        - "PyQt5 GUI框架"
        - "Poetry 依赖管理"
        - "Git for Windows"
        - "Windows 10+ 操作系统"
        - "GitHub 代码托管平台"
        - "GitHub Actions 自动构建和发布"
    devices:
        - "USBCANFD-200U"
        - "USBCANFD-100U"
        - "USBCANFD-MINI"

rule "development-environment":
    description: "开发环境规范"
    requirements:
        - "使用用户自定义的bashrc环境管理"
        - "Poetry虚拟环境管理"
        - "Pre-commit代码质量检查"
        - "Black代码格式化"
        - "Ruff代码检查"
    workflow:
        - "pyshell - 进入开发环境"
        - "pyformat - 格式化代码"
        - "pytest - 运行测试"
        - "pyrun <命令> - 在虚拟环境中执行命令"

rule "code-style":
    description: "代码风格规范"
    principles:
        - "保持代码简洁、可读"
        - "使用有意义的变量和函数名"
        - "添加适当的注释解释复杂逻辑"
        - "如果原来的代码没有清晰的注释，请完善代码注释"
        - "遵循PEP 8 Python代码风格指南"
        - "使用类型提示提高代码质量"
        - "编写详细的docstring文档"
    formatting:
        - "使用Black自动格式化，行长度88字符"
        - "使用Ruff进行代码检查和修复"
        - "提交前自动运行质量检查"

rule "project-structure":
    description: "项目结构规范"
    principles:
        - "保持项目结构清晰，遵循模块化原则"
        - "同类功能应放在同一目录下"
        - "使用适当的目录命名，反映其包含内容"
        - "遵循既定的架构模式"
    structure:
        - "api/ - 核心API（只读）"
        - "src/ - 源代码"
        - "tests/ - 测试代码"
        - "config/ - 配置文件"
        - "logs/ - 日志文件"
        - "resource/ - 资源文件"

rule "development-principles":
    description: "通用开发原则"
    principles:
        - "编写可测试的代码"
        - "避免重复代码（DRY原则）"
        - "优先使用现有库和工具，避免重新发明轮子"
        - "考虑代码的可维护性和可扩展性"
        - "遵循项目既定的架构模式"
        - "使用统一的工具模块（logger、paths、constants等）"
        - "保护核心API文件不被修改"
        - "优先使用Poetry进行依赖管理"

rule "package-management":
    description: "包管理规范"
    manager: "Poetry"
    principles:
        - "始终使用Poetry管理依赖，不手动编辑配置文件"
        - "使用poetry add添加依赖"
        - "使用poetry remove删除依赖"
        - "定期使用poetry update更新依赖"
        - "提交时包含poetry.lock文件"
    rationale: "Poetry自动解决版本冲突，维护依赖一致性，避免手动编辑导致的错误"